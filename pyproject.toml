[project]
name = "MyoGen"
version = "0.1.0"
description = "Surface and Intramuscular EMG simulation toolkit"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "cupy-cuda12x>=13.4.1",
    "impi-rt>=2021.15.0 ; sys_platform == 'win32'",
    "matplotlib>=3.10.1",
    "mpi4py>=4.0.3",
    "neuron>=8.2.6 ; sys_platform == 'linux'",
    "nptyping>=2.5.0",
    "numpy>=1.26",
    "pyneuroml>=1.3.15",
    "pynn>=0.12.4",
    "pyqt6>=6.9.0",
    "scikit-fmm>=2025.1.29",
    "scikit-learn>=1.6.1",
    "scipy>=1.15.3",
    "seaborn>=0.13.2",
    "tqdm>=4.67.1",
]

[tool.ty.rules]
invalid-syntax-in-forward-annotation = "ignore"
possibly-unbound-import = "ignore"
