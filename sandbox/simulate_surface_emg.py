from pathlib import Path
import sys

# Add the project root to Python path
sys.path.append(str(Path(__file__).parent.parent))
import numpy as np

from myogen import generate_spike_trains, generate_fdi_muscle_muaps
from myogen.simulate import simulate_surface_emg
from myogen.utils import plot_input_currents
from myogen.utils.currents import create_trapezoid_current
from myogen.utils.plotting import plot_surface_emg, plot_spike_trains

save_path = Path("./output_100")
save_path.mkdir(parents=True, exist_ok=True)

n_pools = 10
neurons_per_pool = 100
timestep__ms = 0.05  # ms
simulation_time = 2000  # Total simulation time in ms
muap_sampling_frequency = 2048  # Hz
t_points = int(simulation_time / timestep__ms)  # Calculate number of time points
input_current__matrix = create_trapezoid_current(
    n_pools,
    t_points,
    timestep__ms,
    delay_ms=[np.random.uniform(400, 500) for _ in range(n_pools)],
    amplitude_range=[np.random.uniform(200, 300) for _ in range(n_pools)],
    rise_time_ms=[np.random.uniform(200, 300) for _ in range(n_pools)],
    plateau_time_ms=[np.random.uniform(100, 900) for _ in range(n_pools)],
    fall_time_ms=[np.random.uniform(200, 300) for _ in range(n_pools)],
    offset=0,
)
np.save(save_path / "input_current_matrix.npy", input_current__matrix)
plot_input_currents(
    save__path=save_path,
    input_current__matrix=input_current__matrix,
    timestep__ms=timestep__ms,
)

print("Input current matrix generated")

spike_trains__matrix = generate_spike_trains(
    save__path=save_path,
    input_current__matrix=input_current__matrix,
    neurons_per_pool=neurons_per_pool,
)

# spike_trains__matrix = np.load("output/spike_trains.npy")

plot_spike_trains(
    save__path=save_path,
    spike_trains__matrix=spike_trains__matrix,
    timestep__ms=timestep__ms,
)

print("Spike trains matrix generated")
# muap_shapes__tensor, _, _, _ = generate_fdi_muscle_muaps(
#     save__path=save_path, N_MU=neurons_per_pool
# )
muap_shapes__tensor = np.load("output_100/MUAPs.npy")
print("MUAP shapes tensor generated")

# Generate input current matrix
# input_current__matrix =

surface_emg__tensor, spike_trains__matrix, muap_shapes__tensor = simulate_surface_emg(
    save__path=save_path,
    input_current__matrix=input_current__matrix,
    spike_trains__matrix=spike_trains__matrix,
    muap_shapes__tensor=muap_shapes__tensor,
    timestep__ms=timestep__ms,
    muap_sampling_frequency=muap_sampling_frequency,
)
print("Surface EMG tensor generated")
# plot_surface_emg(save__path=save_path, surface_emg__tensor=surface_emg__tensor)
