import numpy as np
from matplotlib import pyplot as plt

surface_emg__tensor = np.load("output_100/surface_emg_per_pool.npy")[:10]

pool_current = np.load("output_100/input_current_matrix.npy")[:10]

snr = 3  # signal to noise ratio in dB

for pool in range(surface_emg__tensor.shape[0]):
    fig, axs = plt.subplots(
        surface_emg__tensor.shape[1],
        surface_emg__tensor.shape[2],
        figsize=(surface_emg__tensor.shape[1], surface_emg__tensor.shape[2]),
        sharex=True,
        num="Pool " + str(pool + 1),
    )
    for row in range(surface_emg__tensor.shape[1]):
        for col in range(surface_emg__tensor.shape[2]):
            temp = surface_emg__tensor[pool, row, col]
            # scale between -1 and 1
            temp = (temp - np.min(temp)) / (np.max(temp) - np.min(temp))
            temp = temp * 2 - 1

            signal_power = np.mean(temp**2)
            noise_power = signal_power / (10 ** (snr / 10))

            axs[row, col].plot(
                temp + np.random.default_rng(180319).normal(0, noise_power, temp.shape),
                label=f"Pool {pool}, Row {row}, Col {col}",
            )

            temp = pool_current[pool]
            # scale between -1 and 1
            temp = (temp - np.min(temp)) / (np.max(temp) - np.min(temp))
            temp = temp

            axs[row, col].plot(
                temp,
                # label=f"Pool {pool}, Row {row}, Col {col} Current",
                linestyle="--",
                color="black",
            )

            # remove spines and ticks
            axs[row, col].spines["top"].set_visible(False)
            axs[row, col].spines["right"].set_visible(False)
            axs[row, col].spines["left"].set_visible(False)
            axs[row, col].spines["bottom"].set_visible(False)
            axs[row, col].tick_params(
                left=False, bottom=False, labelleft=False, labelbottom=False
            )
            # axs[row, col].set_ylim(-1, 1.1)

    plt.tight_layout()
    plt.show()
