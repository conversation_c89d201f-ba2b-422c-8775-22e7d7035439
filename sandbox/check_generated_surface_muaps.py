import numpy as np
from matplotlib import pyplot as plt

muap_shape__tensor = np.load("output_100/MUAPs.npy")[:10]

# for muap_idx in range(muap_shape__tensor.shape[0]):
#
#     # get rows times columns colors from a color map and use them to plot
#     colors = plt.cm.rainbow(
#         np.linspace(0, 1, muap_shape__tensor.shape[1] * muap_shape__tensor.shape[2])
#     )
#
#     for row_idx in range(muap_shape__tensor.shape[1]):
#         for col_idx in range(muap_shape__tensor.shape[2]):
#             plt.plot(
#                 muap_shape__tensor[muap_idx, row_idx, col_idx],
#                 label=f"MUAP {muap_idx}, Row {row_idx}, Col {col_idx}",
#                 alpha=1,
#                 color=colors[row_idx * muap_shape__tensor.shape[2] + col_idx],
#                 linewidth=2,
#             )
#             plt.show()
#     mean_muap = muap_shape__tensor[muap_idx].mean(axis=0).mean(axis=0)
#     plt.plot(mean_muap, label=f"Mean MUAP {muap_idx}", color="black", linewidth=5)
#     plt.title(f"MUAP {muap_idx}")
#     plt.show()

# center the muap_shape__tensor around the center of the muap
# Find the center of each MUAP by finding the index of the maximum absolute value

# Create an empty array to store indices (as lists) of shape (n, r, c)
indices = np.empty(muap_shape__tensor.shape[:-1], dtype=int)

for i in range(indices.shape[0]):
    for j in range(indices.shape[1]):
        for k in range(indices.shape[2]):
            temp = np.where(np.abs(muap_shape__tensor)[i, j, k] >= 1e-21)[0]

            indices[i, j, k] = (temp[-1] - temp[0]) // 2 + temp[0]

centers = indices.mean(axis=(-2, -1)).astype(int)

# Calculate the shift needed to center each MUAP
shifts = muap_shape__tensor.shape[-1] // 2 - centers

# Create a new array to store the centered MUAPs
centered_muap_shape__tensor = np.zeros_like(muap_shape__tensor)

# Shift each MUAP to center it
for muap_idx in range(muap_shape__tensor.shape[0]):
    for row_idx in range(muap_shape__tensor.shape[1]):
        for col_idx in range(muap_shape__tensor.shape[2]):
            # Get the shift for this specific MUAP
            shift = shifts[muap_idx]

            # Shift the MUAP
            if shift > 0:
                # Shift right
                centered_muap_shape__tensor[muap_idx, row_idx, col_idx, shift:] = (
                    muap_shape__tensor[muap_idx, row_idx, col_idx, :-shift]
                )
            elif shift < 0:
                # Shift left
                centered_muap_shape__tensor[muap_idx, row_idx, col_idx, :shift] = (
                    muap_shape__tensor[muap_idx, row_idx, col_idx, -shift:]
                )
            else:
                # No shift needed
                centered_muap_shape__tensor[muap_idx, row_idx, col_idx] = (
                    muap_shape__tensor[muap_idx, row_idx, col_idx]
                )

# Replace the original tensor with the centered one
muap_shape__tensor = centered_muap_shape__tensor

# Find the non-zero region of each MUAP

# Find significant regions across all MUAPs
threshold = 0.01 * np.max(np.abs(muap_shape__tensor))
significant_indices = np.where(np.abs(muap_shape__tensor) > threshold)[
    -1
]  # Get last dimension indices

# Get the global start and end indices
start_idx = np.min(significant_indices)
end_idx = np.max(significant_indices)

# Add padding (10% of the signal length)
padding = int(0.1 * muap_shape__tensor.shape[-1])
start_idx = max(0, start_idx - padding)
end_idx = min(muap_shape__tensor.shape[-1], end_idx + padding)

# Slice the entire tensor at once
muap_shape__tensor = muap_shape__tensor[..., start_idx:end_idx]

# Now all MUAPs have been sliced to their significant regions


for muap_idx in range(muap_shape__tensor.shape[0]):
    fig, axs = plt.subplots(
        muap_shape__tensor.shape[1],
        muap_shape__tensor.shape[2],
        figsize=(muap_shape__tensor.shape[2], muap_shape__tensor.shape[1]),
        sharex=True,
        num="MUAP " + str(muap_idx),
    )

    # fig.suptitle(f"MUAP {muap_idx}", fontsize=16)

    min: float = np.min(muap_shape__tensor[muap_idx])
    max: float = np.max(muap_shape__tensor[muap_idx])

    for row_idx in range(muap_shape__tensor.shape[1]):
        for col_idx in range(muap_shape__tensor.shape[2]):
            axs[row_idx, col_idx].plot(
                muap_shape__tensor[muap_idx, row_idx, col_idx],
                alpha=1,
                linewidth=2,
            )
            # axs[row_idx, col_idx].set_title(f"R{row_idx} C{col_idx}")
            # remove all ticks and axes
            axs[row_idx, col_idx].set_xticks([])
            axs[row_idx, col_idx].set_yticks([])
            # axs[row_idx, col_idx].set_xlabel("Time (ms)")
            # axs[row_idx, col_idx].set_ylabel("Amplitude (mV)")

            # remove all spines
            axs[row_idx, col_idx].spines["top"].set_visible(False)
            axs[row_idx, col_idx].spines["right"].set_visible(False)
            axs[row_idx, col_idx].spines["left"].set_visible(False)
            axs[row_idx, col_idx].spines["bottom"].set_visible(False)

            # set the x and y limits to the min and max of the muap_shape__tensor
            axs[row_idx, col_idx].set_ylim(min, max)

    plt.tight_layout(rect=[0, 0.03, 1, 0.95])
plt.show()

# for each muap plot the mean per column
for muap_idx in range(muap_shape__tensor.shape[0]):
    colors = plt.cm.rainbow(np.linspace(0, 1, muap_shape__tensor.shape[1]))

    mean_muap = muap_shape__tensor[muap_idx].mean(axis=1)
    mean_muap = np.sort(mean_muap, axis=0)

    fig = plt.figure(
        figsize=(muap_shape__tensor.shape[2], muap_shape__tensor.shape[1]),
        dpi=100,
        num="MUAP " + str(muap_idx),
    )
    for row_idx in range(mean_muap.shape[0]):
        plt.plot(
            mean_muap[row_idx],
            label=f"{row_idx}",
            alpha=1,
            color=colors[row_idx],
            linewidth=2,
        )

    plt.legend(title="Row # Mean")
    # plt.title(f"MUAP {muap_idx}")
plt.show()

for muap_idx in range(muap_shape__tensor.shape[0]):
    colors = plt.cm.rainbow(np.linspace(0, 1, muap_shape__tensor.shape[2]))

    # sort the muap_shape__tensor by peak 2 peak amplitude along the column axis
    mean_muap = muap_shape__tensor[muap_idx].mean(0)
    mean_muap = np.sort(mean_muap, axis=0)

    fig = plt.figure(
        figsize=(muap_shape__tensor.shape[2], muap_shape__tensor.shape[1]),
        dpi=100,
        num="MUAP " + str(muap_idx),
    )
    for col_idx in range(mean_muap.shape[0]):
        plt.plot(
            mean_muap[col_idx],
            label=f"{col_idx}",
            alpha=1,
            color=colors[col_idx],
            linewidth=2,
        )

    plt.legend(title="Column # Mean")
    # plt.title(f"MUAP {muap_idx}")
plt.show()
