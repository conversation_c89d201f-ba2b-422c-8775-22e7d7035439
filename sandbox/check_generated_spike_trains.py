import numpy as np
from matplotlib import pyplot as plt
import seaborn as sns

spike_trains__matrix = np.load("output_100/spike_trains.npy")
pool_current = np.load("output_100/input_current_matrix.npy")

with sns.plotting_context("talk"):
    for pool in range(spike_trains__matrix.shape[0]):
        index = 0
        fig = plt.figure(num=f"Pool {pool + 1}", figsize=(5, 10), dpi=100)

        first_firings = [
            (i, x[0])
            for i, x in enumerate(
                [
                    np.where(spike_trains__matrix[pool, neuron] == 1)[0]
                    for neuron in range(spike_trains__matrix.shape[1])
                ]
            )
            if len(x) != 0
        ]

        first_firings = sorted(first_firings, key=lambda x: x[1])

        for neuron, _ in first_firings:
            spike = np.where(spike_trains__matrix[pool, neuron] == 1)[0]
            if len(spike) == 0:
                continue
            plt.scatter(
                spike * 0.05 / 1000,  # convert to ms
                np.ones(len(spike)) + index,
                marker="|",
                linewidths=2,
                label=f"Pool {pool}, Neuron {neuron}",
            )
            index += 1

        plt.xlabel("Time (s)")

        plt.ylabel("Neuron #")

        pc = pool_current[pool]

        # scale pc to go from 0 to index
        pc_min = np.min(pc)
        pc_max = np.max(pc)

        pc = (pc - pc_min) / (pc_max - pc_min)
        pc = pc * (index + 1)

        plt.plot(
            np.arange(0, len(pc)) * 0.05 / 1000,
            pc,
            linestyle="--",
            linewidth=2,
            alpha=0.5,
            zorder=0,
            color="red",
            label=f"Pool {pool} Current",
        )

        # add second y axis for current with ticks that go from pc_min to pc_max
        ax2 = plt.gca().twinx()
        # make ax2 red
        ax2.spines["right"].set_color("red")
        ax2.set_ylim(0, index + 1)
        ax2.set_yticks(np.linspace(0, index + 1, 10) * (pc_max - pc_min) / (index + 1) + pc_min)
        ax2.set_ylabel("Current (nA)")

        # set the color of the ticks to red
        ax2.tick_params(axis="y", colors="red")

        # set the color of the label to red
        ax2.yaxis.label.set_color("red")

        sns.despine(trim=True, offset=5, right=False)

        plt.tight_layout()
        plt.show()
