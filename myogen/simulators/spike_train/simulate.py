from pathlib import Path
from typing import Callable

import matplotlib.pyplot as plt
import numpy as np
import pyNN.neuron as sim
from pyNN.neuron.morphology import uniform, centre
from pyNN.parameters import IonicSpecies
from tqdm import tqdm

from myogen.simulators.spike_train import (
    functions,
    classes,
    INPUT_CURRENT__MATRIX,
    SPIKE_TRAIN__MATRIX,
)
from myogen.utils import RANDOM_GENERATOR


def create_motor_neuron_pool(n_neurons: int) -> classes.cell_class:
    """Create a pool of motor neurons with specified parameters

    Parameters
    ----------
    n_neurons: int
        Number of neurons in the pool

    Returns
    -------
    cell_type: classes.cell_class
        Cell type of the motor neuron pool
    """
    rng = RANDOM_GENERATOR
    somas = functions.create_somas(n_neurons)
    dends = functions.create_dends(n_neurons, somas)

    vt = list(
        -70
        + 12.35
        * np.exp(np.arange(n_neurons) / (n_neurons - 1) * np.log(20.9 / 12.35))
        * (1 + 0.05 * rng.normal(size=n_neurons))
    )

    return classes.cell_class(
        morphology=functions.soma_dend(somas, dends),
        cm=1,  # mF / cm**2
        Ra=0.070,  # ohm.mm
        ionic_species={
            "na": IonicSpecies("na", reversal_potential=50),
            "ks": IonicSpecies("ks", reversal_potential=-80),
            "kf": IonicSpecies("kf", reversal_potential=-80),
        },
        pas_soma={"conductance_density": uniform("soma", 7e-4), "e_rev": -70},
        pas_dend={"conductance_density": uniform("dendrite", 7e-4), "e_rev": -70},
        na={
            "conductance_density": uniform("soma", 10),
            "vt": vt,
        },
        kf={
            "conductance_density": uniform("soma", 1),
            "vt": vt,
        },
        ks={
            "conductance_density": uniform("soma", 0.5),
            "vt": vt,
        },
        syn={"locations": centre("dendrite"), "e_syn": 0, "tau_syn": 0.6},
    )


def create_simulation_progress_callback(
    total_time_ms: float, update_interval_ms: float = 10
) -> Callable[[float], float]:
    """
    Creates a tqdm-based callback function for PyNN simulations.

    Parameters
    ----------
    total_time_ms : float
        Total simulation time in milliseconds
    update_interval_ms : float
        Update interval in milliseconds (how often to update the progress bar)

    Returns
    -------
    callable
        A callback function that can be passed to sim.run()
    """
    progress_bar = tqdm(total=total_time_ms, unit="ms", desc="Simulation progress")
    last_time = 0.0

    def progress_callback(current_time):
        nonlocal last_time
        # Update the progress bar with the time elapsed since last call
        progress_bar.update(np.round(current_time - last_time, 2).astype(float))
        last_time = current_time

        # Determine if we've reached the end of simulation
        if current_time >= total_time_ms:
            progress_bar.close()
            return None  # Return None to indicate no further callbacks

        # Return the next time to be called
        next_time = min(current_time + update_interval_ms, total_time_ms)
        return next_time

    return progress_callback


def generate_spike_trains(
    save__path: Path,
    input_current__matrix: INPUT_CURRENT__MATRIX,
    neurons_per_pool: int = 100,
    timestep__ms: float = 0.05,
    noise_mean__nA: float = 30,  # noqa N803
    noise_stdev__nA: float = 30,  # noqa N803
) -> SPIKE_TRAIN__MATRIX:
    """
    Generate the spike trains for as many neuron pools as input currents there are

    Each motor neuron pools have each "neurons_per_pool" neurons.
    The input currents are injected into each pool, and the spike trains are recorded.

    Following data is stored under "save__path":
    - spikes_neo.pkl: spike trains for all neurons in each pool
    - v_neo.pkl: membrane potentials for the first two neurons in each pool
    - spike_trains.npy: spike trains of every pool as a matrix of shape (n_pools, neurons_per_pool, t_points)
    - input_currents.png: plot of the input currents
    - spike_trains.png: plot of the spike trains

    Parameters
    ----------
    save__path: Path
        Path to save the simulation results
    input_current__matrix : INPUT_CURRENT__MATRIX
        Matrix of shape (n_pools, t_points) containing current values
        Each row represents the current for one pool
    neurons_per_pool : int
        Number of neurons in each pool
    timestep__ms : float
        Simulation timestep__ms in ms
    noise_mean__nA : float
        Mean of the noise current in nA
    noise_stdev__nA : float
        Standard deviation of the noise current in nA

    Returns
    -------
    spike_trains: SPIKE_TRAIN__MATRIX
        Matrix of shape (n_pools, neurons_per_pool, t_points) containing spike trains
        Each row represents the spike train for one pool
        Each column represents the spike train for one neuron
        Each element represents whether the neuron spiked at that time point
    """
    sim.setup(timestep=timestep__ms)

    # Create motor neuron pools
    pools: list[sim.Population] = []
    for _ in range(len(input_current__matrix)):
        cell_type = create_motor_neuron_pool(neurons_per_pool)
        pool = sim.Population(neurons_per_pool, cell_type, initial_values={"v": -70})
        pools.append(pool)

    # Inject currents into each pool - one current per pool
    times = np.arange(input_current__matrix.shape[-1]) * timestep__ms
    for input_current, pool in zip(input_current__matrix, pools):
        current_source = sim.StepCurrentSource(times=times, amplitudes=input_current)
        current_source.inject_into(pool, location="soma")

        # Add Gaussian noise current to each neuron in the pool
        for neuron in pool:
            noise_source = sim.NoisyCurrentSource(
                mean=noise_mean__nA,
                stdev=noise_stdev__nA,
                start=0,
                stop=times[-1] + timestep__ms,
                dt=timestep__ms,
            )
            noise_source.inject_into([neuron], location="soma")

    # Set up recording
    for pool in pools:
        pool.record("spikes", to_file=str(save__path / "spikes_neo.pkl"))
        pool.record(
            "v", locations=("dendrite", "soma"), to_file=str(save__path / "v_neo.pkl")
        )

    # Run simulation
    sim.run(input_current__matrix.shape[-1] * timestep__ms)
    sim.end()

    # Convert spike times to binary arrays and save
    time_indices = np.arange(input_current__matrix.shape[-1])
    spike_trains = np.array(
        [
            [
                np.isin(time_indices, np.array(st / timestep__ms).astype(int))
                for st in pool.get_data().segments[0].spiketrains
            ]
            for pool in pools
        ]
    )
    np.save(save__path / "spike_trains.npy", spike_trains)

    return spike_trains


if __name__ == "__main__":
    from myogen.utils import plot_input_currents
    from myogen.utils.currents import create_trapezoid_current
    from myogen.utils.plotting import plot_spike_trains

    # Simulation parameters
    neurons_per_pool = 100
    n_pools = 5

    timestep = 0.05  # ms
    simulation_time = 2000  # Total simulation time in ms

    noise_mean = 26  # Mean noise current (nA) % 100
    noise_stdev = 20  # Standard deviation of noise (nA) % 40

    # Create input current matrix
    t_points = int(simulation_time / timestep)  # Calculate number of time points

    input_current__matrix = create_trapezoid_current(
        n_pools,
        t_points,
        timestep,
        amplitude_range=[np.random.uniform(100, 200) for _ in range(n_pools)],
        rise_time_ms=[np.random.uniform(100, 500) for _ in range(n_pools)],
        plateau_time_ms=[np.random.uniform(100, 500) for _ in range(n_pools)],
        fall_time_ms=[np.random.uniform(100, 500) for _ in range(n_pools)],
    )

    save__path = Path("./results")
    save__path.mkdir(exist_ok=True)

    # Run simulation
    spike_trains__matrix = generate_spike_trains(
        save__path,
        input_current__matrix,
        neurons_per_pool=neurons_per_pool,
        timestep__ms=timestep,
        noise_mean__nA=noise_mean,
        noise_stdev__nA=noise_stdev,
    )

    # Plot and save input currents
    plot_spike_trains(
        spike_trains__matrix=spike_trains__matrix,
        timestep__ms=timestep,
        save__path=save__path,
    )
    plot_input_currents(
        save__path=save__path,
        input_current__matrix=input_current__matrix,
        timestep__ms=timestep,
    )
    plt.show()
