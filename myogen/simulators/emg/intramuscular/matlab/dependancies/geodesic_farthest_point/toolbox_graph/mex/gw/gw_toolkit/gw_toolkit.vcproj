<?xml version="1.0" encoding = "Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.00"
	Name="gw_toolkit"
	ProjectGUID="{09368184-7AEC-43BF-AD0C-3DF9417CCC25}"
	Keyword="Win32Proj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="obj/Debug"
			IntermediateDirectory="obj/Debug"
			ConfigurationType="4"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions="/Zm200"
				Optimization="0"
				AdditionalIncludeDirectories="../external/"
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				MinimalRebuild="TRUE"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="3"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="4"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="../bin/gw_toolkit_dbg.lib"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="obj/Release"
			IntermediateDirectory="obj/Release"
			ConfigurationType="4"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				AdditionalOptions="/Zm200"
				Optimization="2"
				InlineFunctionExpansion="1"
				OmitFramePointers="TRUE"
				AdditionalIncludeDirectories="../external/"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="3"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="3"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="../bin/gw_toolkit.lib"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
		</Configuration>
	</Configurations>
	<Files>
		<Filter
			Name="Stdafx"
			Filter="">
			<File
				RelativePath="stdafx.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="stdafx.h">
			</File>
		</Filter>
		<Filter
			Name="Displayer"
			Filter="">
			<File
				RelativePath="GW_BasicDisplayer.cpp">
			</File>
			<File
				RelativePath="GW_BasicDisplayer.h">
			</File>
			<File
				RelativePath="GW_BasicDisplayer.inl">
			</File>
			<Filter
				Name="Geodesic Displayer"
				Filter="">
				<File
					RelativePath="GW_GeodesicDisplayer.cpp">
				</File>
				<File
					RelativePath="GW_GeodesicDisplayer.h">
				</File>
				<File
					RelativePath="GW_GeodesicDisplayer.inl">
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Toolkit"
			Filter="">
			<File
				RelativePath="GW_Toolkit.cpp">
			</File>
			<File
				RelativePath="GW_Toolkit.h">
			</File>
		</Filter>
		<Filter
			Name="IO"
			Filter="">
			<File
				RelativePath="GW_InputOutput.cpp">
			</File>
			<File
				RelativePath="GW_InputOutput.h">
			</File>
			<Filter
				Name="Trakball"
				Filter="">
				<File
					RelativePath="trackball.cpp">
				</File>
				<File
					RelativePath="trackball.h">
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Loaders"
			Filter="">
			<Filter
				Name="VRML Loader"
				Filter="">
				<File
					RelativePath="GW_VRMLLoader.cpp">
				</File>
				<File
					RelativePath="GW_VRMLLoader.h">
				</File>
			</Filter>
			<Filter
				Name="PLY Loader"
				Filter="">
				<File
					RelativePath="GW_PLYLoader.cpp">
				</File>
				<File
					RelativePath="GW_PLYLoader.h">
				</File>
				<Filter
					Name="PLY"
					Filter="">
					<File
						RelativePath="ply\ply.c">
						<FileConfiguration
							Name="Debug|Win32">
							<Tool
								Name="VCCLCompilerTool"
								UsePrecompiledHeader="0"/>
						</FileConfiguration>
						<FileConfiguration
							Name="Release|Win32">
							<Tool
								Name="VCCLCompilerTool"
								UsePrecompiledHeader="0"/>
						</FileConfiguration>
					</File>
					<File
						RelativePath="ply\ply.h">
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="ASE Loader"
				Filter="">
				<File
					RelativePath="GW_ASELoader.cpp">
				</File>
				<File
					RelativePath="GW_ASELoader.h">
				</File>
			</Filter>
			<Filter
				Name="OFF Loader"
				Filter="">
				<File
					RelativePath="GW_OFFLoader.cpp">
				</File>
				<File
					RelativePath="GW_OFFLoader.h">
				</File>
			</Filter>
			<Filter
				Name="CSV Loader"
				Filter="">
				<File
					RelativePath="GW_CSVLoader.h">
				</File>
			</Filter>
			<Filter
				Name="OBJLoader"
				Filter="">
				<File
					RelativePath="GW_OBJLoader.cpp">
				</File>
				<File
					RelativePath="GW_OBJLoader.h">
				</File>
			</Filter>
		</Filter>
		<File
			RelativePath="GW_OpenGLHelper.h">
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
