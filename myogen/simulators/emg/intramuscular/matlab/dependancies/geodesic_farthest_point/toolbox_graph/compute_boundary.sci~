function boundary=compute_boundary(face, options)

// compute_boundary - compute the vertices on the boundary of a 3D mesh
//
//   boundary=compute_boundary(face);
//
//   Copyright (c) 2007 <PERSON>

if size(face,1)<size(face,2)
    face=face';
end



if size(face,1)<size(face,2)
    face=face';
end

//// compute edges (i,j) that are adjacent to only 1 face
A = compute_edge_face_ring(face);
[i,j,v] = find(A);
i = i(v==-1);
j = j(v==-1);

//// build the boundary by traversing the edges
boundary = i(1); i(1) = []; j(1) = [];
while not(isempty(i))
    b = boundary($);
    I = find(i==b);
    if isempty(I)
        I = find(j==b);
        if isempty(I)
            warning('Problem with boundary');
            break;
        end
        boundary($+1) = i(I);
    else
        boundary($+1) = j(I);
    end        
	i(I) = []; j(I) = [];
end


endfunction

