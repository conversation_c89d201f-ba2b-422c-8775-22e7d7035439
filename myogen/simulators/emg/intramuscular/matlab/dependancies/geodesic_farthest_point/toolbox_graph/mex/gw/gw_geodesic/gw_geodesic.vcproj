<?xml version="1.0" encoding = "Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.00"
	Name="gw_geodesic"
	ProjectGUID="{B48BCFEF-BF2D-4907-901A-7EBBC29DF6FB}"
	Keyword="Win32Proj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="obj/Debug"
			IntermediateDirectory="obj/Debug"
			ConfigurationType="4"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="../external/"
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				MinimalRebuild="TRUE"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="3"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="4"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="../bin/gw_geodesic_dbg.lib"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="obj/Release"
			IntermediateDirectory="obj/Release"
			ConfigurationType="4"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				OmitFramePointers="TRUE"
				AdditionalIncludeDirectories="../external/"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="3"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="3"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="../bin/gw_geodesic.lib"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
		</Configuration>
	</Configurations>
	<Files>
		<Filter
			Name="Stdafx"
			Filter="">
			<File
				RelativePath="stdafx.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="stdafx.h">
			</File>
		</Filter>
		<Filter
			Name="Triangulation structure"
			Filter="">
			<Filter
				Name="Vertex"
				Filter="">
				<File
					RelativePath="GW_GeodesicVertex.cpp">
				</File>
				<File
					RelativePath="GW_GeodesicVertex.h">
				</File>
				<File
					RelativePath="GW_GeodesicVertex.inl">
				</File>
			</Filter>
			<Filter
				Name="Mesh"
				Filter="">
				<File
					RelativePath="GW_GeodesicMesh.cpp">
				</File>
				<File
					RelativePath="GW_GeodesicMesh.h">
				</File>
				<File
					RelativePath="GW_GeodesicMesh.inl">
				</File>
			</Filter>
			<Filter
				Name="Face"
				Filter="">
				<File
					RelativePath="GW_GeodesicFace.cpp">
				</File>
				<File
					RelativePath="GW_GeodesicFace.h">
				</File>
				<File
					RelativePath="GW_GeodesicFace.inl">
				</File>
				<Filter
					Name="Interpolation"
					Filter="">
					<File
						RelativePath="GW_TriangularInterpolation_ABC.h">
					</File>
					<Filter
						Name="Quadratic interpolation"
						Filter="">
						<File
							RelativePath="GW_TriangularInterpolation_Quadratic.cpp">
						</File>
						<File
							RelativePath="GW_TriangularInterpolation_Quadratic.h">
						</File>
						<File
							RelativePath="GW_TriangularInterpolation_Quadratic.inl">
						</File>
					</Filter>
					<Filter
						Name="Linear interpolation"
						Filter="">
						<File
							RelativePath="GW_TriangularInterpolation_Linear.cpp">
						</File>
						<File
							RelativePath="GW_TriangularInterpolation_Linear.h">
						</File>
						<File
							RelativePath="GW_TriangularInterpolation_Linear.inl">
						</File>
					</Filter>
					<Filter
						Name="Cubic interpolation"
						Filter="">
						<File
							RelativePath="GW_TriangularInterpolation_Cubic.cpp">
						</File>
						<File
							RelativePath="GW_TriangularInterpolation_Cubic.h">
						</File>
						<File
							RelativePath="GW_TriangularInterpolation_Cubic.inl">
						</File>
					</Filter>
				</Filter>
			</Filter>
			<Filter
				Name="Path"
				Filter="">
				<File
					RelativePath="GW_GeodesicPath.cpp">
				</File>
				<File
					RelativePath="GW_GeodesicPath.h">
				</File>
				<File
					RelativePath="GW_GeodesicPath.inl">
				</File>
				<Filter
					Name="Geodesic Point"
					Filter="">
					<File
						RelativePath="GW_GeodesicPoint.cpp">
					</File>
					<File
						RelativePath="GW_GeodesicPoint.h">
					</File>
					<File
						RelativePath="GW_GeodesicPoint.inl">
					</File>
				</Filter>
			</Filter>
		</Filter>
		<Filter
			Name="Voronoi"
			Filter="">
			<Filter
				Name="Voronoi Mesh"
				Filter="">
				<File
					RelativePath="GW_VoronoiMesh.cpp">
					<FileConfiguration
						Name="Debug|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalOptions="/Zm500"/>
					</FileConfiguration>
					<FileConfiguration
						Name="Release|Win32">
						<Tool
							Name="VCCLCompilerTool"
							AdditionalOptions="/Zm500"/>
					</FileConfiguration>
				</File>
				<File
					RelativePath="GW_VoronoiMesh.h">
				</File>
				<File
					RelativePath="GW_VoronoiMesh.inl">
				</File>
			</Filter>
			<Filter
				Name="Vornoi vertex"
				Filter="">
				<File
					RelativePath="GW_VoronoiVertex.cpp">
				</File>
				<File
					RelativePath="GW_VoronoiVertex.h">
				</File>
				<File
					RelativePath="GW_VoronoiVertex.inl">
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Parameterization"
			Filter="">
			<File
				RelativePath="GW_Parameterization.cpp">
			</File>
			<File
				RelativePath="GW_Parameterization.h">
			</File>
			<File
				RelativePath="GW_Parameterization.inl">
			</File>
			<Filter
				Name="Geometry cells"
				Filter="">
				<Filter
					Name="Geometry Cell"
					Filter="">
					<File
						RelativePath="GW_GeometryCell.cpp">
					</File>
					<File
						RelativePath="GW_GeometryCell.h">
					</File>
					<File
						RelativePath="GW_GeometryCell.inl">
					</File>
				</Filter>
				<Filter
					Name="Geometry Atlas"
					Filter="">
					<File
						RelativePath="GW_GeometryAtlas.cpp">
					</File>
					<File
						RelativePath="GW_GeometryAtlas.h">
					</File>
					<File
						RelativePath="GW_GeometryAtlas.inl">
					</File>
				</Filter>
			</Filter>
		</Filter>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
