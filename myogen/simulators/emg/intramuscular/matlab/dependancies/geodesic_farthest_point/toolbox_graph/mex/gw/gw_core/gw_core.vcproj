<?xml version="1.0" encoding = "Windows-1252"?>
<VisualStudioProject
	ProjectType="Visual C++"
	Version="7.00"
	Name="gw_core"
	ProjectGUID="{19B2B30E-B24F-4E03-8F40-44ADEB660A93}"
	Keyword="Win32Proj">
	<Platforms>
		<Platform
			Name="Win32"/>
	</Platforms>
	<Configurations>
		<Configuration
			Name="Debug|Win32"
			OutputDirectory="obj/Debug"
			IntermediateDirectory="obj/Debug"
			ConfigurationType="4"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="0"
				AdditionalIncludeDirectories="../external/"
				PreprocessorDefinitions="WIN32;_DEBUG;_LIB"
				MinimalRebuild="TRUE"
				BasicRuntimeChecks="3"
				RuntimeLibrary="3"
				UsePrecompiledHeader="3"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="4"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="../bin/gw_core_dbg.lib"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
		</Configuration>
		<Configuration
			Name="Release|Win32"
			OutputDirectory="obj/Release"
			IntermediateDirectory="obj/Release"
			ConfigurationType="4"
			CharacterSet="2">
			<Tool
				Name="VCCLCompilerTool"
				Optimization="2"
				InlineFunctionExpansion="1"
				OmitFramePointers="TRUE"
				AdditionalIncludeDirectories="../external/"
				PreprocessorDefinitions="WIN32;NDEBUG;_LIB"
				StringPooling="TRUE"
				RuntimeLibrary="2"
				EnableFunctionLevelLinking="TRUE"
				UsePrecompiledHeader="3"
				WarningLevel="3"
				Detect64BitPortabilityProblems="TRUE"
				DebugInformationFormat="3"/>
			<Tool
				Name="VCCustomBuildTool"/>
			<Tool
				Name="VCLibrarianTool"
				OutputFile="../bin/gw_core.lib"/>
			<Tool
				Name="VCMIDLTool"/>
			<Tool
				Name="VCPostBuildEventTool"/>
			<Tool
				Name="VCPreBuildEventTool"/>
			<Tool
				Name="VCPreLinkEventTool"/>
			<Tool
				Name="VCResourceCompilerTool"/>
			<Tool
				Name="VCWebServiceProxyGeneratorTool"/>
		</Configuration>
	</Configurations>
	<Files>
		<Filter
			Name="Maths"
			Filter="">
			<File
				RelativePath="..\gw_maths\GW_Maths.h">
			</File>
			<File
				RelativePath="..\gw_maths\GW_MathsConfig.h">
			</File>
			<File
				RelativePath="GW_MathsWrapper.h">
			</File>
			<Filter
				Name="Vector 3D"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_Vector3D.h">
				</File>
			</Filter>
			<Filter
				Name="Matrix 3D"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_Matrix3x3.h">
				</File>
			</Filter>
			<Filter
				Name="Vector 2D"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_Vector2D.h">
				</File>
			</Filter>
			<Filter
				Name="Matrix NxP"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_MatrixNxP.h">
				</File>
			</Filter>
			<Filter
				Name="Vector ND"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_VectorND.h">
				</File>
			</Filter>
			<Filter
				Name="Sparse matrix"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_SparseMatrix.h">
				</File>
			</Filter>
			<Filter
				Name="Doc"
				Filter="">
				<File
					RelativePath="..\gw_maths\doc\dxt\GWML_MainPage.dxt">
				</File>
			</Filter>
			<Filter
				Name="Vector 4D"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_Vector4D.h">
				</File>
			</Filter>
			<Filter
				Name="Matrix 4x4"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_Matrix4x4.h">
				</File>
			</Filter>
			<Filter
				Name="Vector Static"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_VectorStatic.h">
				</File>
			</Filter>
			<Filter
				Name="Quaternion"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_Quaternion.h">
				</File>
			</Filter>
			<Filter
				Name="Matrix Static"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_MatrixStatic.h">
				</File>
			</Filter>
			<Filter
				Name="Matrix 2x2"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_Matrix2x2.h">
				</File>
			</Filter>
			<Filter
				Name="Complex"
				Filter="">
				<File
					RelativePath="..\gw_maths\GW_Complex.h">
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Stafx"
			Filter="">
			<File
				RelativePath="stdafx.cpp">
				<FileConfiguration
					Name="Debug|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
				<FileConfiguration
					Name="Release|Win32">
					<Tool
						Name="VCCLCompilerTool"
						UsePrecompiledHeader="1"/>
				</FileConfiguration>
			</File>
			<File
				RelativePath="stdafx.h">
			</File>
		</Filter>
		<Filter
			Name="Utils"
			Filter="">
			<Filter
				Name="Smart Counter"
				Filter="">
				<File
					RelativePath="GW_SmartCounter.cpp">
				</File>
				<File
					RelativePath="GW_SmartCounter.h">
				</File>
				<File
					RelativePath="GW_SmartCounter.inl">
				</File>
			</Filter>
			<Filter
				Name="Serializable"
				Filter="">
				<File
					RelativePath="GW_Serializable.h">
				</File>
			</Filter>
			<Filter
				Name="Polygon intersector"
				Filter="">
				<File
					RelativePath="GW_PolygonIntersector.h">
				</File>
			</Filter>
			<Filter
				Name="Progress Bar"
				Filter="">
				<File
					RelativePath="GW_ProgressBar.h">
				</File>
			</Filter>
		</Filter>
		<Filter
			Name="Doc"
			Filter="">
			<File
				RelativePath="..\doc\dxt\GW_Download.dxt">
			</File>
			<File
				RelativePath="..\doc\dxt\GW_GalleryGeodesic.dxt">
			</File>
			<File
				RelativePath="..\doc\dxt\GW_GalleryOperators.dxt">
			</File>
			<File
				RelativePath="..\doc\dxt\GW_GalleryParameterization.dxt">
			</File>
			<File
				RelativePath="..\doc\dxt\GW_GallerySegmentation.dxt">
			</File>
			<File
				RelativePath="..\doc\dxt\GW_GallerySpherical.dxt">
			</File>
			<File
				RelativePath="..\doc\dxt\GW_Internals.dxt">
			</File>
			<File
				RelativePath="..\doc\dxt\GW_MainPage.dxt">
			</File>
			<File
				RelativePath="..\doc\dxt\GW_MathsBackground.dxt">
			</File>
			<File
				RelativePath="..\doc\dxt\GW_ResultsSpherical.dxt">
			</File>
		</Filter>
		<Filter
			Name="Triangulation strutures"
			Filter="">
			<Filter
				Name="Face"
				Filter="">
				<File
					RelativePath="GW_Face.cpp">
				</File>
				<File
					RelativePath="GW_Face.h">
				</File>
				<File
					RelativePath="GW_Face.inl">
				</File>
				<Filter
					Name="Iterator"
					Filter="">
					<File
						RelativePath="GW_FaceIterator.cpp">
					</File>
					<File
						RelativePath="GW_FaceIterator.h">
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="Vertex"
				Filter="">
				<File
					RelativePath="GW_Vertex.cpp">
				</File>
				<File
					RelativePath="GW_Vertex.h">
				</File>
				<File
					RelativePath="GW_Vertex.inl">
				</File>
				<Filter
					Name="Iterator"
					Filter="">
					<File
						RelativePath="GW_VertexIterator.cpp">
					</File>
					<File
						RelativePath="GW_VertexIterator.h">
					</File>
				</Filter>
			</Filter>
			<Filter
				Name="Mesh"
				Filter="">
				<File
					RelativePath="GW_Mesh.cpp">
				</File>
				<File
					RelativePath="GW_Mesh.h">
				</File>
				<File
					RelativePath="GW_Mesh.inl">
				</File>
			</Filter>
		</Filter>
		<File
			RelativePath="GW_Config.cpp">
		</File>
		<File
			RelativePath="GW_Config.h">
		</File>
	</Files>
	<Globals>
	</Globals>
</VisualStudioProject>
