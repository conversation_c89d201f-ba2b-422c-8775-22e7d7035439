function extend_stack_size(mu)

// extend_stack_size - extend memory
//
//  extend_stack_size(mult);
//
//  Copyright (c) 2008 <PERSON>


global stacksize_global;
if isempty(stacksize_global)
    stacksize_global = 1;
end

if argn(2)<1
    mu = 4;
end

smax = 20;
if stacksize_global>smax
    warning(strcat(['Stacksize multiplier extend ' num2str(smax) '.']));
    warning(strcat(['Solution: do not call extend_stack_size too many time or extend global .']));
    return;
end

stacksize_global = stacksize_global*mu;

a = stacksize();
stacksize(mu*a(1));

endfunction