import numpy as np
import matplotlib.pyplot as plt
from myogen.simulators.emg.intramuscular.python.classes import (
    MN_Pool_Sim,
    MU_Pool_Sim,
    IntramuscularArray,
    MU_Sim,
    MuscleForceMdl,
)

# Simulation parameters (reduced for speed)
N = 10  # Number of motor units
fs = 10240  # Sampling frequency
duration = 2.0  # Simulation duration in seconds
T = int(duration * fs)

print("Creating minimal EMG simulation...")

# 1. Create motor neuron pool
mn_pool = MN_Pool_Sim(N=N, rr=50, rm=0.75)
mn_pool.distribute_innervation_centers(Rmuscle=2.0)  # Small muscle
mn_pool.generate_minfr("linear_rt", -5, 10)
mn_pool.generate_maxfr("linear_rt", -10, 40)
mn_pool.generate_frs("linear_rt", -20, 50)
mn_pool.CV = 1 / 7  # Coefficient of variation

# 2. Create motor unit pool
mu_pool = MU_Pool_Sim(mn_pool)
mu_pool.generate_mfs(Rmuscle=2.0, Dmf=400)  # Very low fiber density
mu_pool.calc_innervation_areas()
mu_pool.calc_innervation_numbers()  # Required before assign_mfs2mns
mu_pool.assign_mfs2mns(fast_mode=False)
mu_pool.generate_mf_diameters()
mu_pool.generate_mf_cvs()

# 3. Create simple force model
force_model = MuscleForceMdl(N=N, size_magnitude=30, hfs=fs)

# Normalize force model with MVC
print("Normalizing force model...")
mvc_T = int(2 * fs)  # Short MVC measurement
mvc_excitation = np.ones(mvc_T)
mvc_spikes, _, _ = mn_pool.generate_spike_train_gauss(
    np.arange(1, mvc_T + 1), np.full(N, np.nan), mvc_excitation, fs
)
mvc_force, fmax = force_model.normalize_mvc(mvc_spikes)
print(f"Maximum force: {fmax:.6f}")

# 4. Create electrode array
electrode = IntramuscularArray(n_electrodes=2, spacing=0.5)

# 5. Generate excitation profile (ramp up)
timeline = np.arange(T) / fs
excitation = np.minimum(timeline / 1.0, 0.5)  # Ramp to 50% in 1 second

# 6. Generate spike trains
spikes, _, _ = mn_pool.generate_spike_train_gauss(
    np.arange(1, T + 1), np.full(N, np.nan), excitation, fs
)

# 7. Generate MUAPs for each motor unit
MUs = []
for mu_idx in range(N):
    # Get motor unit fibers
    fiber_indices = np.where(mu_pool.assignment == mu_idx)[0]
    if len(fiber_indices) > 0:
        mf_centers = mu_pool.mf_centers[fiber_indices, :]
        mf_diameters = mu_pool.mf_diameters[fiber_indices]
        mf_cv = mu_pool.mf_cv[fiber_indices]
        branch_v = np.array([5000, 2000])  # Branch velocities [mm/s]

        mu_sim = MU_Sim(
            mf_centers=mf_centers,
            mf_index=mu_idx,
            Lmuscle=30.0,  # Muscle length [mm]
            mf_diameters=mf_diameters,
            mf_cv=mf_cv,
            branch_v=branch_v,
            nominal_center=mn_pool.centers[mu_idx, :]
            if hasattr(mn_pool, "centers")
            else None,
        )

        # Simulate NMJ branches
        mu_sim.sim_nmj_branches_two_layers(
            n_branches=1
            + int(np.log(mu_pool.mn_pool.sz[mu_idx] / mu_pool.mn_pool.sz[0])),
            endplate_area_center=15.0,  # Center of muscle
            branches_z_std=1.5,
            arborization_z_std=0.5,
        )

        # Calculate SFAPs and MUAP
        mu_sim.calc_sfaps(dt=1 / fs, dz=0.1, electrode_pts=electrode.pts)
        mu_sim.calc_muap(jitter_std=0.0)

        # Store original MUAP for visualization
        mu_sim.muap_original = mu_sim.muap.copy() if mu_sim.muap is not None else None

        # Apply differential recording to get proper EMG signal
        if mu_sim.muap is not None:
            mu_sim.muap = mu_sim.muap @ electrode.diff_mat.T
    else:
        # Create empty MU for units with no fibers
        mu_sim = None

    MUs.append(mu_sim)

# 8. Generate EMG by convolving spikes with MUAPs
emg = np.zeros((T, electrode.n_channels))
for mu_idx in range(N):
    if MUs[mu_idx] is not None and MUs[mu_idx].muap is not None:
        for ch in range(electrode.n_channels):
            # Convolve spike train with MUAP
            muap_signal = np.convolve(
                spikes[:, mu_idx], MUs[mu_idx].muap[:, ch], mode="same"
            )
            emg[:, ch] += muap_signal

# 9. Generate force signal
force = force_model.generate_force_offline(spikes, prefix="Generating force:")

# 10. Create visualizations
fig, axes = plt.subplots(2, 2, figsize=(15, 10))

# Plot excitation and force
axes[0, 0].plot(timeline, excitation, "b-", label="Excitation", linewidth=2)
axes[0, 0].plot(timeline, force, "r-", label="Force", linewidth=2)
axes[0, 0].set_xlabel("Time (s)")
axes[0, 0].set_ylabel("Normalized amplitude")
axes[0, 0].set_title("Excitation and Force")
axes[0, 0].legend()
axes[0, 0].grid(True)

# Plot spike raster
spike_times, spike_units = np.where(spikes)
axes[0, 1].scatter(spike_times / fs, spike_units, s=20, alpha=1)
axes[0, 1].set_xlabel("Time (s)")
axes[0, 1].set_ylabel("Motor Unit")
axes[0, 1].set_title("Spike Raster Plot")
axes[0, 1].grid(True)

# Plot EMG signals
for ch in range(electrode.n_channels):
    axes[1, 0].plot(timeline, emg[:, ch] + ch * 0.1, label=f"Channel {ch + 1}")
axes[1, 0].set_xlabel("Time (s)")
axes[1, 0].set_ylabel("EMG Amplitude")
axes[1, 0].set_title("EMG Signals")
axes[1, 0].legend()
axes[1, 0].grid(True)

# Plot MUAP examples - show MUAPs for motor units that are actually firing
spike_times, spike_units = np.where(spikes)
active_mus = np.unique(spike_units)  # Get motor units that fired spikes
muap_plotted = False

print(f"Active motor units (firing spikes): {active_mus}")
print(
    f"Motor units with MUAPs: {[i for i in range(N) if MUs[i] is not None]}"
)

for mu_idx in active_mus:  # Show all active MUAPs
    if (
        MUs[mu_idx] is not None
        and hasattr(MUs[mu_idx], "muap_original")
        and MUs[mu_idx].muap_original is not None
    ):
        muap_time = (
            np.arange(MUs[mu_idx].muap_original.shape[0]) / fs * 1000
        )  # Convert to ms
        # Plot each MUAP clearly without offset
        axes[1, 1].plot(
            muap_time,
            MUs[mu_idx].muap_original[:, 0],
            label=f"MU {mu_idx + 1}",
            linewidth=2,
            alpha=0.8,
        )
        muap_plotted = True

if not muap_plotted:
    # If no MUAPs plotted, show a message
    axes[1, 1].text(
        0.5,
        0.5,
        "No MUAPs available for active motor units",
        transform=axes[1, 1].transAxes,
        ha="center",
        va="center",
    )

axes[1, 1].set_xlabel("Time (ms)")
axes[1, 1].set_ylabel("MUAP Amplitude (mV)")
axes[1, 1].set_title("Motor Unit Action Potentials")
axes[1, 1].legend()
axes[1, 1].grid(True)

plt.tight_layout()
plt.savefig("minimal_emg_simulation.png", dpi=150, bbox_inches="tight")
plt.show()

print(f"\n✓ Minimal EMG simulation completed!")
print(f"Motor units: {N}")
print(f"Muscle fibers: {mu_pool.Nmf}")
print(f"Simulation duration: {duration} seconds")
print(f"Sampling frequency: {fs} Hz")
print(f"EMG channels: {electrode.n_channels}")
print(f"Total spikes generated: {np.sum(spikes)}")
print(f"EMG signal shape: {emg.shape}")
print(f"Peak force: {np.max(force):.3f}")
