"""
Select detectable MUAPs

Annotation should contain only those MUs, who's APs are prominent in the
signal and, thus, decomposable. In fact, prominence is a tricky notion,
since small MU's AP are prominent at their recruitment level and are not
prominent at higher excitation due to appearance of larger MUs;
So, prominence of a MUAP should be:
1) Assessed at its MU recruitment threshold;
2) Compared to the instrumentation noise.

First criterion: by explained variance of the signal at the recruitment
threshold

Power of motor unit's signal is calculated as it's
MUAP's energy times the firing rate at recruitment.
Contribution is then calculated as percentage of that power in the total
power of the emg signal at that excitation level.
"""

import numpy as np

print("Selecting detectable MUAPs...")

# Parameters
total_explained_variance = 0.95
over_noise = 6

# Initialize arrays
recr_contribution = np.zeros(mu_pool.N)
expl_detectable = np.zeros(mu_pool.N, dtype=bool)
expl_detectable[0] = True  # First MU is always detectable

# Explained variance criterion
for m in range(1, mu_pool.N):
    fr = np.zeros(m + 1)
    explained_variance = np.zeros((m + 1, electrode.n_channels))

    # Get firing rates and powers of all MUs that are active at this
    # excitation level (basically, all smaller MUs)
    for i in range(m + 1):
        # Excitation should be > rt to activate the motor unit
        fr[i] = mu_pool.mn_pool.calculate_fr(
            mu_pool.mn_pool.rt[i] + np.finfo(float).eps, i
        )

        # Calculate MUAP sum of squares (simplified for Python - no trajectory mixing)
        muap_processed = MUs[i].muap @ electrode.diff_mat.T
        muap_sum_squares = np.sum(muap_processed**2, axis=0)
        explained_variance[i, :] = muap_sum_squares * fr[i]

    # Normalize contributions
    total_variance = np.sum(explained_variance, axis=0)
    explained_variance = explained_variance / total_variance[np.newaxis, :]

    # Check if this MU contributes significantly
    expl_detectable[m] = np.any(
        explained_variance[m, :] > (1 - total_explained_variance)
    )

# Prominence criterion: by prominence compared to instrumentation noise
prom_detectable = np.zeros(mu_pool.N, dtype=bool)
for m in range(mu_pool.N):
    muap_diff = MUs[m].muap @ electrode.diff_mat.T
    max_amplitude = np.max(np.abs(muap_diff))
    prom_detectable[m] = np.any(max_amplitude > over_noise * emg_noise_std)

# Combine both criteria
detectable = prom_detectable & expl_detectable
detectable_ind = np.where(detectable)[0]
expl_detectable_ind = np.where(expl_detectable)[0]
prom_detectable_ind = np.where(prom_detectable)[0]

print(f"Total motor units: {mu_pool.N}")
print(f"Detectable by explained variance: {len(expl_detectable_ind)}")
print(f"Detectable by prominence: {len(prom_detectable_ind)}")
print(f"Detectable by both criteria: {len(detectable_ind)}")
print(f"Detectable motor unit indices: {detectable_ind}")

# Clean up temporary variables
del muap_sum_squares, recr_contribution, over_noise, total_explained_variance
