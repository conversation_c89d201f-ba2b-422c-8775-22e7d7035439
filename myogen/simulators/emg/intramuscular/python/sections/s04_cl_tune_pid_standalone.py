"""
Standalone PID Controller Tuning Functions for Excitation-Force Model.
This version can be imported without dependency checks.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from scipy import signal
from scipy.optimize import minimize
import warnings

# Use non-interactive backend for server environments
matplotlib.use("Agg")

try:
    from myogen.simulators.emg.intramuscular.python.config import fs, fsl
except ImportError:
    # Fallback values for standalone testing
    fs = 10000  # Hz
    fsl = 50  # Hz


def safe_lsim(tf_system, input_data, time_vector):
    """
    Safely handle signal.lsim which can return 2 or 3 values.

    Returns:
    --------
    tuple : (time_vector, output_data)
        Always returns exactly 2 values regardless of system representation
    """
    result = signal.lsim(tf_system, input_data, time_vector)
    if len(result) == 2:
        return result
    else:  # len(result) == 3
        return result[0], result[1]  # Return time and output, ignore state


def multilevel_prbs(length, max_freq, amplitude=1, levels=2):
    """
    Generate a pseudo-random binary sequence (PRBS) similar to MATLAB's multilevel_prbs.

    Parameters:
    -----------
    length : int
        Length of the sequence
    max_freq : float
        Maximum frequency content
    amplitude : float
        Amplitude of the sequence
    levels : int
        Number of levels (default 2 for binary)

    Returns:
    --------
    numpy.ndarray
        Pseudo-random binary sequence
    """
    # Generate a random sequence and filter it
    np.random.seed(42)  # For reproducibility
    random_signal = np.random.randn(length)

    # Design a low-pass filter to limit frequency content
    nyquist = fs / 2
    cutoff = min(max_freq, nyquist * 0.8)
    sos = signal.butter(4, cutoff / nyquist, btype="low", output="sos")
    filtered_signal = signal.sosfilt(sos, random_signal)

    # Quantize to binary levels
    threshold = np.median(filtered_signal)
    prbs = np.where(filtered_signal > threshold, amplitude, 0)

    return prbs


def resample_data(data, original_fs, target_fs):
    """
    Resample data from original_fs to target_fs.

    Parameters:
    -----------
    data : numpy.ndarray
        Input data to resample
    original_fs : float
        Original sampling frequency
    target_fs : float
        Target sampling frequency

    Returns:
    --------
    numpy.ndarray
        Resampled data
    """
    if original_fs == target_fs:
        return data

    # Calculate resampling ratio
    resample_ratio = target_fs / original_fs
    new_length = int(len(data) * resample_ratio)

    # Use scipy's resample function
    resampled_data = signal.resample(data, new_length)

    return resampled_data


def estimate_oe_model(input_data, output_data, nb=1, nf=1, nk=0):
    """
    Estimate an Output-Error (OE) model using optimization.

    Parameters:
    -----------
    input_data : numpy.ndarray
        Input signal
    output_data : numpy.ndarray
        Output signal
    nb : int
        Number of B polynomial coefficients
    nf : int
        Number of F polynomial coefficients
    nk : int
        Input delay

    Returns:
    --------
    dict
        Dictionary containing model parameters
    """

    def oe_cost_function(params):
        """Cost function for OE model estimation."""
        b_coeffs = params[:nb]
        f_coeffs = np.concatenate([[1], params[nb : nb + nf]])

        # Create transfer function
        try:
            tf_system = signal.TransferFunction(b_coeffs, f_coeffs)

            # Simulate the system
            time_vector = np.arange(len(input_data)) / fsl
            _, y_pred = safe_lsim(tf_system, input_data, time_vector)

            # Calculate cost (mean squared error)
            cost = np.mean((output_data - y_pred) ** 2)

        except:
            cost = 1e6  # Large penalty for invalid parameters

        return cost

    # Initial parameter guess
    initial_params = np.concatenate(
        [
            np.random.randn(nb) * 0.1,  # B coefficients
            np.random.randn(nf) * 0.1,  # F coefficients (excluding f0=1)
        ]
    )

    # Optimize parameters
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        result = minimize(oe_cost_function, initial_params, method="L-BFGS-B")

    # Extract optimized parameters
    b_coeffs = result.x[:nb]
    f_coeffs = np.concatenate([[1], result.x[nb : nb + nf]])

    # Create final transfer function
    tf_system = signal.TransferFunction(b_coeffs, f_coeffs)

    return {
        "tf": tf_system,
        "b": b_coeffs,
        "f": f_coeffs,
        "cost": result.fun,
        "success": result.success,
    }


def predict_model(model_tf, input_data, prediction_horizon=0):
    """
    Predict model output given input data.

    Parameters:
    -----------
    model_tf : scipy.signal.TransferFunction
        Transfer function model
    input_data : numpy.ndarray
        Input signal
    prediction_horizon : int
        Prediction horizon (0 for simulation)

    Returns:
    --------
    numpy.ndarray
        Predicted output
    """
    time_vector = np.arange(len(input_data)) / fsl
    _, y_pred = safe_lsim(model_tf, input_data, time_vector)

    return y_pred


def tune_pid_controller(plant_tf, controller_type="PI", bandwidth=8):
    """
    Tune a PID controller for the given plant.

    Parameters:
    -----------
    plant_tf : scipy.signal.TransferFunction
        Plant transfer function
    controller_type : str
        Type of controller ('P', 'PI', 'PID')
    bandwidth : float
        Desired bandwidth

    Returns:
    --------
    dict
        Dictionary containing PID parameters
    """
    # Simple PID tuning based on pole placement
    # This is a simplified version of MATLAB's pidtune

    # Get plant characteristics
    poles = plant_tf.poles
    zeros = plant_tf.zeros

    # Calculate DC gain manually: G(0) = num(0) / den(0)
    try:
        dc_gain = (
            plant_tf.num[-1] / plant_tf.den[-1]
        )  # Last coefficients are constant terms
    except (ZeroDivisionError, IndexError):
        dc_gain = 1.0  # Default fallback

    # Simple tuning rules (Ziegler-Nichols inspired)
    if controller_type.upper() == "PI":
        # For PI controller
        Kp = bandwidth / abs(dc_gain)
        Ki = Kp * bandwidth / 4
        Kd = 0
    elif controller_type.upper() == "PID":
        # For PID controller
        Kp = bandwidth / abs(dc_gain)
        Ki = Kp * bandwidth / 4
        Kd = Kp / (4 * bandwidth)
    else:  # Proportional only
        Kp = bandwidth / abs(dc_gain)
        Ki = 0
        Kd = 0

    return {"Kp": Kp, "Ki": Ki, "Kd": Kd}


def create_pid_tf(pid_params):
    """
    Create a PID controller transfer function.

    Parameters:
    -----------
    pid_params : dict
        Dictionary with Kp, Ki, Kd values

    Returns:
    --------
    scipy.signal.TransferFunction
        PID controller transfer function
    """
    Kp, Ki, Kd = pid_params["Kp"], pid_params["Ki"], pid_params["Kd"]

    # PID transfer function: Kp + Ki/s + Kd*s
    # In discrete time or using approximation
    if Ki == 0 and Kd == 0:
        # Proportional only
        return signal.TransferFunction([Kp], [1])
    elif Kd == 0:
        # PI controller: Kp + Ki/s = (Kp*s + Ki)/s
        return signal.TransferFunction([Kp, Ki], [1, 0])
    else:
        # Full PID: Kp + Ki/s + Kd*s = (Kd*s² + Kp*s + Ki)/s
        return signal.TransferFunction([Kd, Kp, Ki], [1, 0])


def feedback_system(controller_tf, plant_tf):
    """
    Create a closed-loop feedback system.

    Parameters:
    -----------
    controller_tf : scipy.signal.TransferFunction
        Controller transfer function
    plant_tf : scipy.signal.TransferFunction
        Plant transfer function

    Returns:
    --------
    scipy.signal.TransferFunction
        Closed-loop transfer function
    """
    # Forward path: controller * plant
    forward_tf = signal.TransferFunction(
        np.convolve(controller_tf.num, plant_tf.num),
        np.convolve(controller_tf.den, plant_tf.den),
    )

    # Closed-loop: T = G/(1+G) where G is the forward path
    # T(s) = N(s) / (D(s) + N(s))
    forward_num = forward_tf.num
    forward_den = forward_tf.den

    # Ensure numerator and denominator have the same length by padding with zeros
    max_len = max(len(forward_num), len(forward_den))

    # Pad the shorter array with leading zeros
    if len(forward_num) < max_len:
        forward_num = np.concatenate(
            [np.zeros(max_len - len(forward_num)), forward_num]
        )
    if len(forward_den) < max_len:
        forward_den = np.concatenate(
            [np.zeros(max_len - len(forward_den)), forward_den]
        )

    # Closed-loop transfer function: T(s) = N(s) / (D(s) + N(s))
    closed_loop_num = forward_num
    closed_loop_den = forward_den + forward_num

    return signal.TransferFunction(closed_loop_num, closed_loop_den)
