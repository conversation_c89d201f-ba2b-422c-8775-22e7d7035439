"""
Script s08_cl_init_muaps.py
Initialize Motor Unit Action Potentials (MUAPs) for intramuscular EMG simulation.

This script:
1. Defines MU parameters (NMJ jitter, branch velocities)
2. Initializes MU objects using the MU_Sim class
3. Generates neuromuscular junction coordinates distribution
4. Calculates SFAPs (Single Fiber Action Potentials)
5. Calculates MUAP templates
6. Creates visualizations

Translated from MATLAB script s08_cl_init_muaps.m
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import time

# Use non-interactive backend for server environments
matplotlib.use("Agg")

from myogen.simulators.emg.intramuscular.python.classes.MU_Sim import MU_Sim
from myogen.simulators.emg.intramuscular.python.config import N, Lmuscle, fs, dt, dz

# Ensure required variables exist from previous scripts
try:
    mu_pool
    electrode
except NameError:
    raise NameError(
        "Required variables not found. Please run previous scripts in sequence:\n"
        "- s01_cl_init_mn_pool.py\n"
        "- s02_cl_init_mu_pool.py\n"
        "- s03_cl_init_force_model.py\n"
        "- s04_cl_tune_pid.py\n"
        "- s07_cl_init_electrode.py"
    )

print("Initializing Motor Unit Action Potentials (MUAPs)...")
print("=" * 60)

## Define MU parameters
print("Setting up MU parameters...")

# NMJ jitter standard deviation (default value from Hamilton Stashuk)
nmj_jitter = 35e-6  # seconds

# NMJ branch conduction velocity [mm/s]
# 5000mm/s from Pr. Yann Pereon
branch_v = np.array([5000, 2000])  # [axon_velocity, branch_velocity]

print(f"NMJ jitter: {nmj_jitter * 1e6:.1f} μs")
print(f"Branch velocities: {branch_v} mm/s")

## MU objects initialization
print(f"\nInitializing {N} Motor Unit objects...")
start_time = time.time()

# Initialize MU array
MUs = []

for i in range(N):
    print(f"Initializing MU {i + 1}/{N}...", end="\r")

    # Get muscle fibers for this motor unit
    fiber_mask = mu_pool.assignment == i
    mu_mf_centers = mu_pool.mf_centers[fiber_mask, :]
    mu_mf_diameters = mu_pool.mf_diameters[fiber_mask]
    mu_mf_cv = mu_pool.mf_cv[fiber_mask]

    # Handle motor units with no assigned fibers
    if len(mu_mf_centers) == 0:
        print(f"Warning: MU {i + 1} has no assigned muscle fibers")
        # Create dummy arrays to avoid errors
        mu_mf_centers = np.empty((0, 2))
        mu_mf_diameters = np.empty(0)
        mu_mf_cv = np.empty(0)

    # Create MU_Sim object
    mu_sim = MU_Sim(
        mf_centers=mu_mf_centers,
        mf_index=i,
        Lmuscle=Lmuscle,
        mf_diameters=mu_mf_diameters,
        mf_cv=mu_mf_cv,
        branch_v=branch_v,
        nominal_center=mu_pool.mn_pool.centers[i, :],
    )

    MUs.append(mu_sim)

print(f"\n✓ {N} Motor Units initialized in {time.time() - start_time:.2f}s")

## Generate neuromuscular junction coordinates distribution
print("\nGenerating neuromuscular junction distributions...")
start_time = time.time()

endplate_area_center = Lmuscle / 2  # Center of endplate area
# Number of branches based on motor unit size (logarithmic relationship)
n_branches = 1 + np.round(np.log(mu_pool.mn_pool.sz / mu_pool.mn_pool.sz[0]))

for i in range(N):
    print(f"Processing MU {i + 1}/{N} NMJ distribution...", end="\r")

    # Calculate standard deviations based on motor unit hierarchy
    # Larger motor units have more spread in their NMJ distribution
    size_ratio = np.sum(mu_pool.mn_pool.sz[: i + 1]) / np.sum(mu_pool.mn_pool.sz)

    arborization_z_std = 0.5 + size_ratio * 1.5
    branches_z_std = 1.5 + size_ratio * 4

    # Generate NMJ distribution using two-layer model
    MUs[i].sim_nmj_branches_two_layers(
        int(n_branches[i]), endplate_area_center, branches_z_std, arborization_z_std
    )

print(f"\n✓ NMJ distributions generated in {time.time() - start_time:.2f}s")

## Calculate MU SFAPs (Single Fiber Action Potentials)
print("\nCalculating SFAPs (this may take several minutes)...")
print("Progress:")

total_start_time = time.time()
for i in range(N):
    sfap_start_time = time.time()

    # Calculate SFAPs for this motor unit
    MUs[i].calc_sfaps(dt, dz, electrode.pts, electrode.normals)

    sfap_time = time.time() - sfap_start_time
    elapsed_time = time.time() - total_start_time
    estimated_total = elapsed_time * N / (i + 1)
    remaining_time = estimated_total - elapsed_time

    print(
        f"MU {i + 1:3d}/{N}: {sfap_time:5.1f}s | "
        f"Elapsed: {elapsed_time:6.1f}s | "
        f"Remaining: {remaining_time:6.1f}s"
    )

total_sfap_time = time.time() - total_start_time
print(f"\n✓ All SFAPs calculated in {total_sfap_time:.1f}s")

## Calculate MUAP templates (no jitter)
print("\nCalculating MUAP templates...")
start_time = time.time()

for i in range(N):
    print(f"Calculating MUAP {i + 1}/{N}...", end="\r")
    MUs[i].calc_muap(0)  # No jitter for templates

print(f"\n✓ MUAP templates calculated in {time.time() - start_time:.2f}s")

# Find maximum MUAP length for consistent plotting
max_muap_len = 0
for i in range(N):
    if MUs[i].muap.shape[0] > max_muap_len:
        max_muap_len = MUs[i].muap.shape[0]

print(
    f"Maximum MUAP length: {max_muap_len} samples ({max_muap_len / fs * 1000:.1f} ms)"
)

## Create visualizations
print("\nCreating MUAP visualizations...")

# Plot MUAPs in several common axes (better for comparison)
fig = plt.figure(figsize=(12, 8))
fig.suptitle(
    "Motor Unit Action Potentials (MUAPs)\nOrdered from smallest to largest MU",
    fontsize=14,
)

side = int(np.ceil(np.sqrt(N)))
plot_duration = 10 / 1000  # 10 ms
plot_samples = int(plot_duration * fs)

for row in range(side):
    muap_to_plot = []
    labels = []

    for col in range(side):
        mu_idx = row * side + col
        if mu_idx < N:
            # Get MUAP data
            muap_data = MUs[mu_idx].muap @ electrode.diff_mat.T

            # Limit to plot duration
            n_samples = min(plot_samples, muap_data.shape[0])
            muap_to_plot.append(muap_data[:n_samples, :])
            labels.append(f"MU {mu_idx + 1}")

    if muap_to_plot:  # Only create subplot if there's data
        ax = plt.subplot(side, 1, row + 1)

        # Concatenate MUAPs for this row
        combined_muap = (
            np.vstack(muap_to_plot) if len(muap_to_plot) > 1 else muap_to_plot[0]
        )
        timeline = np.arange(combined_muap.shape[0]) / fs * 1000  # ms

        # Plot all channels
        for ch in range(combined_muap.shape[1]):
            plt.plot(
                timeline,
                combined_muap[:, ch],
                alpha=0.8,
                label=f"Ch {ch + 1}" if row == 0 else None,
            )

        plt.xlabel("Time (ms)" if row == side - 1 else "")
        plt.ylabel("Amplitude (μV)")
        plt.grid(True, alpha=0.3)

        if row == 0 and electrode.n_channels > 1:
            plt.legend(loc="upper right")

plt.tight_layout()
plt.savefig("muaps_visualization.png", dpi=150, bbox_inches="tight")
plt.close()
print("Saved MUAPs visualization to muaps_visualization.png")

# Optional: Create individual MUAP plots for detailed inspection
if N <= 16:  # Only for reasonable number of MUs
    print("Creating individual MUAP plots...")

    fig, axes = plt.subplots(4, 4, figsize=(16, 12))
    fig.suptitle("Individual Motor Unit Action Potentials", fontsize=16)

    for i in range(min(16, N)):
        row = i // 4
        col = i % 4
        ax = axes[row, col]

        muap_data = MUs[i].muap @ electrode.diff_mat.T
        n_samples = min(plot_samples, muap_data.shape[0])
        timeline = np.arange(n_samples) / fs * 1000

        # Plot all channels
        for ch in range(muap_data.shape[1]):
            ax.plot(
                timeline,
                muap_data[:n_samples, ch],
                label=f"Ch {ch + 1}" if electrode.n_channels > 1 else None,
            )

        ax.set_title(f"MU {i + 1} (Size: {mu_pool.mn_pool.sz[i]:.2f})")
        ax.set_xlabel("Time (ms)")
        ax.set_ylabel("Amplitude (μV)")
        ax.grid(True, alpha=0.3)

        if electrode.n_channels > 1 and i == 0:
            ax.legend()

    # Hide unused subplots
    for i in range(N, 16):
        row = i // 4
        col = i % 4
        axes[row, col].set_visible(False)

    plt.tight_layout()
    plt.savefig("individual_muaps.png", dpi=150, bbox_inches="tight")
    plt.close()
    print("Saved individual MUAPs to individual_muaps.png")

## Optional: Demonstrate jitter effect
if N > 0:
    print("\nDemonstrating jitter effect...")

    # Select a representative motor unit (around middle size)
    demo_mu_idx = N // 2

    fig, ax = plt.subplots(figsize=(10, 6))

    # Plot original MUAP (no jitter) in black
    timeline = np.arange(plot_samples) / fs * 1000
    muap_original = MUs[demo_mu_idx].muap @ electrode.diff_mat.T
    muap_original = muap_original[:plot_samples, :]

    # Plot only first channel for clarity
    ax.plot(timeline, muap_original[:, 0], "k-", linewidth=2, label="Original MUAP")

    # Plot multiple MUAPs with jitter
    for i in range(10):
        muap_jitter = MUs[demo_mu_idx].calc_muap(nmj_jitter) @ electrode.diff_mat.T
        muap_jitter = muap_jitter[:plot_samples, :]
        ax.plot(
            timeline,
            muap_jitter[:, 0],
            "g-",
            linewidth=0.5,
            alpha=0.7,
            label="With jitter" if i == 0 else "",
        )

    # Plot original again on top
    ax.plot(timeline, muap_original[:, 0], "k-", linewidth=2)

    ax.set_xlabel("Time (ms)")
    ax.set_ylabel("Amplitude (μV)")
    ax.set_title(
        f"Jitter Effect on MUAP (MU {demo_mu_idx + 1})\n"
        f"Jitter std: {nmj_jitter * 1e6:.0f} μs"
    )
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig("jitter_demonstration.png", dpi=150, bbox_inches="tight")
    plt.close()
    print("Saved jitter demonstration to jitter_demonstration.png")

# Statistics
total_fibers = sum(len(mu.mf_centers) for mu in MUs)
avg_fibers_per_mu = total_fibers / N if N > 0 else 0
fiber_counts = [len(mu.mf_centers) for mu in MUs]
non_empty_mus = [count for count in fiber_counts if count > 0]

print("\n" + "=" * 60)
print("MUAP initialization completed successfully!")
print(f"✓ {N} Motor Units with action potentials ready")
print(
    f"✓ Electrode configuration: {electrode.n_electrodes} electrodes, {electrode.n_channels} channels"
)
print(f"✓ Total muscle fibers: {mu_pool.Nmf}")
print(f"✓ SFAP calculation time: {total_sfap_time:.1f}s")
print(f"✓ Maximum MUAP duration: {max_muap_len / fs * 1000:.1f} ms")
print("✓ All MUAP templates are ready for EMG simulation")

print(f"\nStatistics:")
print(f"• Average fibers per MU: {avg_fibers_per_mu:.1f}")
print(f"• Smallest MU: {min(fiber_counts)} fibers")
print(f"• Largest MU: {max(fiber_counts)} fibers")
if non_empty_mus:
    print(f"• Non-empty MUs: {len(non_empty_mus)}/{N}")
    print(f"• Min fibers (non-empty): {min(non_empty_mus)} fibers")
    print(f"• Max fibers (non-empty): {max(non_empty_mus)} fibers")
print(f"• NMJ jitter: {nmj_jitter * 1e6:.0f} μs")
print(f"• Branch velocities: [5000, 2000] mm/s")

# Clean up variables as in MATLAB version
del branch_v
