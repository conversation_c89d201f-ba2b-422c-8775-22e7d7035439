"""
Electrode initialization and visualization script.

This script sets up different electrode configurations for intramuscular EMG simulation
and creates visualization plots showing the electrode positions relative to the muscle
geometry and motor unit centers.

Translated from MATLAB script s07_cl_init_electrode.m
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import matplotlib.patches as patches

# Use non-interactive backend for server environments
matplotlib.use("Agg")

from myogen.simulators.emg.intramuscular.python.classes.IntramuscularArray import (
    IntramuscularArray,
)
from myogen.simulators.emg.intramuscular.python.config import Rmuscle, Lmuscle

# Ensure mu_pool exists from previous script
try:
    mu_pool
except NameError:
    raise NameError(
        "mu_pool not found. Please run s02_cl_init_mu_pool.py first to create the motor unit pool."
    )

print("Initializing electrode configuration...")

## Set up the electrode (uncomment one of the choices below)

# End-to-end array
# electrode = IntramuscularArray(11, 1, 'consecutive')
# electrode.set_position([-Rmuscle, 0, 2*Lmuscle/3], [-np.pi/2, 0, -np.pi/2])
# electrode.set_linear_trajectory(0, 1)  # Static

# One-step scanning array
# electrode = IntramuscularArray(16, 1, 'consecutive')
# electrode.set_position([-Rmuscle, 0, Lmuscle/2+5], [-np.pi/6, 0, -np.pi/2])
# electrode.set_linear_trajectory(1, 1)  # One-step pull out

# Single channel differential electrode
electrode = IntramuscularArray(2, 0.5)
electrode.set_position(
    np.array([0, 0, 2 * Lmuscle / 3]) - np.array([0.5, 0, 0]),
    [-np.pi / 2, 0, -np.pi / 2],
)
electrode.set_linear_trajectory(0.125, 4)

# Two-channel differential electrode
# electrode = IntramuscularArray(3, 0.5)
# electrode.set_position([-2.5, 0, 2*Lmuscle/3], [-np.pi/2, 0, -np.pi/2])

# Scanning electrode
# electrode = IntramuscularArray(2, 1)
# electrode.set_position([-Rmuscle, 0, 2*Lmuscle/3], [-np.pi/2, 0, -np.pi/2])
# electrode.set_linear_trajectory(2*Rmuscle-1, 19)

print(
    f"Electrode configuration: {electrode.n_electrodes} electrodes with {electrode.spacing}mm spacing"
)
print(f"Electrode type: {electrode.arrangement}")
print(
    f"Trajectory: {electrode.trajectory_distance}mm over {electrode.trajectory_steps} steps"
)

## Plot muscle and the electrode

# Create muscle cross-section circle
x_circle = np.linspace(-Rmuscle, Rmuscle, 1000)
y_circle_pos = np.sqrt(Rmuscle**2 - x_circle**2)
x_circle = np.concatenate([x_circle, x_circle[::-1]])
y_circle = np.concatenate([y_circle_pos, -y_circle_pos[::-1]])

# Create figure with subplots
fig = plt.figure(figsize=(15, 9))

# Cross-section view
ax1 = plt.subplot(2, 2, 1)
plt.plot(x_circle, y_circle, "k-", linewidth=1)

# Plot motor unit centers with labels
for i in range(mu_pool.N):
    plt.text(
        mu_pool.mn_pool.centers[i, 0],
        mu_pool.mn_pool.centers[i, 1],
        str(i + 1),
        fontsize=8,
        ha="center",
        va="center",
    )

plt.axis("equal")
saved_xlim = plt.xlim()
saved_ylim = plt.ylim()

# Plot current electrode positions
electrode_positions = electrode.get_electrode_positions()
electrode_initial = electrode.get_initial_positions()

for i in range(electrode_positions.shape[0]):
    plt.plot(electrode_positions[i, 0], electrode_positions[i, 1], "k.", markersize=8)

for i in range(electrode_initial.shape[0]):
    plt.plot(electrode_initial[i, 0], electrode_initial[i, 1], "bo", markersize=6)

plt.title("Cross-section view")
plt.xlabel("Height, mm")
plt.ylabel("Width, mm")
plt.grid(True, alpha=0.3)

# Top view
ax3 = plt.subplot(2, 2, 3)
# Draw muscle rectangle (top view)
muscle_rect = patches.Rectangle(
    (-Rmuscle, 0), 2 * Rmuscle, Lmuscle, linewidth=1, edgecolor="k", facecolor="none"
)
plt.gca().add_patch(muscle_rect)

# Plot electrode positions in top view
for i in range(electrode_positions.shape[0]):
    plt.plot(electrode_positions[i, 0], electrode_positions[i, 2], "k.", markersize=8)

for i in range(electrode_initial.shape[0]):
    plt.plot(electrode_initial[i, 0], electrode_initial[i, 2], "bo", markersize=6)

plt.xlim(saved_xlim)
plt.title("Top view")
plt.xlabel("Width, mm")
plt.ylabel("Length, mm")
plt.axis("equal")
plt.grid(True, alpha=0.3)

# Left view
ax2 = plt.subplot(2, 2, 2)
# Draw muscle rectangle (left view)
muscle_rect_left = patches.Rectangle(
    (0, -Rmuscle), Lmuscle, 2 * Rmuscle, linewidth=1, edgecolor="k", facecolor="none"
)
plt.gca().add_patch(muscle_rect_left)

# Plot electrode positions in left view with different markers for each electrode
colors = plt.cm.tab10(np.linspace(0, 1, electrode.n_points))
for i in range(electrode.n_points):
    # Plot all positions for this electrode point
    electrode_z = (
        electrode_positions[i :: electrode.n_points, 2]
        if electrode_positions.shape[0] > electrode.n_points
        else [electrode_positions[i, 2]]
    )
    electrode_y = (
        electrode_positions[i :: electrode.n_points, 1]
        if electrode_positions.shape[0] > electrode.n_points
        else [electrode_positions[i, 1]]
    )
    plt.plot(
        electrode_z,
        electrode_y,
        "*",
        color=colors[i],
        markersize=8,
        label=f"Electrode {i + 1}",
    )

plt.title("Left view")
plt.xlabel("Length, mm")
plt.ylabel("Width, mm")
plt.axis("equal")
plt.grid(True, alpha=0.3)
if electrode.n_points > 1:
    plt.legend()

# Add a text box with electrode information
ax4 = plt.subplot(2, 2, 4)
ax4.axis("off")
info_text = f"""Electrode Configuration:
• Number of electrodes: {electrode.n_electrodes}
• Spacing: {electrode.spacing} mm
• Type: {electrode.arrangement}
• Trajectory distance: {electrode.trajectory_distance} mm
• Trajectory steps: {electrode.trajectory_steps}

Muscle Parameters:
• Radius: {Rmuscle:.1f} mm
• Length: {Lmuscle:.1f} mm
• Motor units: {mu_pool.N}
• Muscle fibers: {mu_pool.Nmf}

Legend:
• Black dots: Current electrode positions
• Blue circles: Initial electrode positions
• Numbers: Motor unit centers (cross-section)
"""
ax4.text(
    0.05,
    0.95,
    info_text,
    transform=ax4.transAxes,
    fontsize=10,
    verticalalignment="top",
    fontfamily="monospace",
    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightgray", alpha=0.8),
)

plt.tight_layout()
plt.savefig("electrode_configuration.png", dpi=150, bbox_inches="tight")
plt.close()

print("Saved electrode configuration plot to electrode_configuration.png")

# Create a detailed cross-section view with all motor units
fig2, ax = plt.subplots(figsize=(10, 10))

# Plot muscle boundary
circle = patches.Circle(
    (0, 0), Rmuscle, linewidth=2, edgecolor="black", facecolor="lightblue", alpha=0.3
)
ax.add_patch(circle)

# Plot motor unit innervation centers
scatter = ax.scatter(
    mu_pool.mn_pool.centers[:, 0],
    mu_pool.mn_pool.centers[:, 1],
    c=range(mu_pool.N),
    cmap="viridis",
    s=50,
    alpha=0.7,
    edgecolors="black",
    linewidth=0.5,
)

# Add motor unit labels
for i in range(mu_pool.N):
    ax.annotate(
        f"{i + 1}",
        (mu_pool.mn_pool.centers[i, 0], mu_pool.mn_pool.centers[i, 1]),
        xytext=(3, 3),
        textcoords="offset points",
        fontsize=6,
        ha="left",
    )

# Plot electrode positions
for i, pos in enumerate(electrode_positions):
    ax.plot(
        pos[0],
        pos[1],
        "ro",
        markersize=10,
        markeredgecolor="darkred",
        markeredgewidth=2,
        label=f"Electrode {i + 1}" if i < 5 else "",
    )

for i, pos in enumerate(electrode_initial):
    ax.plot(
        pos[0],
        pos[1],
        "bs",
        markersize=8,
        markeredgecolor="darkblue",
        markeredgewidth=2,
        label=f"Initial {i + 1}" if i < 5 else "",
    )

ax.set_xlim(-Rmuscle * 1.1, Rmuscle * 1.1)
ax.set_ylim(-Rmuscle * 1.1, Rmuscle * 1.1)
ax.set_aspect("equal")
ax.grid(True, alpha=0.3)
ax.set_xlabel("X Position (mm)", fontsize=12)
ax.set_ylabel("Y Position (mm)", fontsize=12)
ax.set_title(
    "Detailed Cross-Section View: Motor Units and Electrode Positions", fontsize=14
)

# Add colorbar for motor units
cbar = plt.colorbar(scatter, ax=ax, shrink=0.8)
cbar.set_label("Motor Unit Index", fontsize=12)

# Add legend for electrodes
handles, labels = ax.get_legend_handles_labels()
if handles:
    ax.legend(
        handles[: min(len(handles), 10)],
        labels[: min(len(labels), 10)],
        loc="upper right",
        bbox_to_anchor=(1.15, 1),
    )

plt.tight_layout()
plt.savefig("detailed_cross_section.png", dpi=150, bbox_inches="tight")
plt.close()

print("Saved detailed cross-section plot to detailed_cross_section.png")

print(f"\nElectrode initialization completed successfully!")
print(f"Electrode positions (current):")
for i, pos in enumerate(electrode_positions):
    print(f"  Electrode {i + 1}: [{pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}] mm")

print(f"\nElectrode positions (initial):")
for i, pos in enumerate(electrode_initial):
    print(f"  Electrode {i + 1}: [{pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f}] mm")

# Clean up as in MATLAB version
del x_circle, y_circle, y_circle_pos
