"""
Script s10_cl_generate_emg.py
Generate EMG signals according to the predefined contraction profile.

This script:
1. Generates EMG signals using spike trains and MUAPs
2. Applies trajectory mixing and differential recording
3. Adds realistic noise to the signals

Translated from MATLAB script s10_cl_generate_emg.m
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import time

# Use non-interactive backend for server environments
matplotlib.use("Agg")

from myogen.simulators.emg.intramuscular.python.config import fs

# Ensure required variables exist from previous scripts
try:
    spikes
    mu_pool
    electrode
    MUs
    max_muap_len
    emg_noise_std
except NameError:
    raise NameError(
        "Required variables not found. Please run previous scripts in sequence:\n"
        "- s01_cl_init_mn_pool.py\n"
        "- s02_cl_init_mu_pool.py\n"
        "- s03_cl_init_force_model.py\n"
        "- s04_cl_tune_pid.py\n"
        "- s05_cl_init_profile.py\n"
        "- s06_cl_generate_force.py\n"
        "- s07_cl_init_electrode.py\n"
        "- s08_cl_init_muaps.py\n"
        "- s09_cl_generate_mvc_emg.py"
    )

print("Generating EMG signals according to contraction profile...")
print("=" * 60)

## Set neuromuscular junction jitter
nmj_jitter = (
    0  # 35e-6; # Neuromuscular junction jitter (set to 0 for deterministic output)
)

## Generate EMG according to profile
print("Initializing EMG signal arrays...")
start_time = time.time()

# Initialize EMG signal array - match MATLAB: electrode.n_points * electrode.n_nodes
# Shape: (time_samples, electrode_points * electrode_nodes)
emg = np.zeros((spikes.shape[0], electrode.n_points * electrode.n_nodes))
T = spikes.shape[0]
traj_parameter_map = np.linspace(0, 1, T)

print(f"✓ EMG array initialized: {emg.shape}")
print(f"✓ Signal duration: {T} samples ({T / fs:.2f}s)")

## Generate MUAPs for each motor unit and spike time
print("\nGenerating MUAP trains...")
generation_start_time = time.time()

for i in range(mu_pool.N):
    mu_start_time = time.time()
    spike_count = 0

    # Find all spike times for this motor unit to show progress
    spike_times = np.where(spikes[:, i])[0]

    for t in range(T - max_muap_len - 1):
        if spikes[t, i]:
            # Calculate MUAP for this motor unit with jitter
            muap_to_add = MUs[i].calc_muap(nmj_jitter)
            muap_len = muap_to_add.shape[0]

            # Add MUAP to EMG signal (ensure we don't exceed array bounds)
            end_idx = min(T, t + muap_len)
            muap_end_idx = end_idx - t

            emg[t:end_idx, :] += muap_to_add[:muap_end_idx, :]
            spike_count += 1

    mu_time = time.time() - mu_start_time
    print(
        f"✓ MU {i + 1:3d}/{mu_pool.N}: {spike_count} spikes processed in {mu_time:.2f}s"
    )

generation_time = time.time() - generation_start_time
print(f"\n✓ MUAP generation completed in {generation_time:.1f}s")

## Apply electrode signal processing - match MATLAB implementation
print("\nApplying electrode signal processing...")
processing_start_time = time.time()

# Initialize clear EMG array for final channels
emg_clear = np.zeros((T, electrode.n_channels))

# Apply trajectory mixing and differential recording for each time point
# This matches the MATLAB implementation exactly
for i in range(T):
    # Get trajectory mixing matrix for this time point
    # electrode.traj_mixing_mat(traj_parameter_map[i], electrode.n_nodes, electrode.n_channels)
    if hasattr(electrode, "traj_mixing_mat"):
        # If trajectory mixing is available, use it
        mixing_mat = electrode.traj_mixing_mat(
            traj_parameter_map[i], electrode.n_nodes, electrode.n_channels
        )
        emg_clear[i, :] = emg[i, :] @ mixing_mat.T @ electrode.diff_mat.T
    else:
        # Simplified approach if trajectory mixing is not available
        # Reshape emg to separate nodes and points, then average over nodes
        emg_reshaped = emg[i, :].reshape(electrode.n_points, electrode.n_nodes)
        emg_averaged = np.mean(emg_reshaped, axis=1)  # Average over nodes
        emg_clear[i, :] = emg_averaged @ electrode.diff_mat.T

processing_time = time.time() - processing_start_time
print(f"✓ Signal processing completed in {processing_time:.2f}s")

## Add noise to EMG signals
print("\nAdding noise to EMG signals...")
noise_start_time = time.time()

# Generate noise with same shape as clear EMG
# MATLAB: emg_noise = repmat(emg_noise_std, T, 1) .* randn(size(emg_clear))
emg_noise = np.tile(emg_noise_std, (T, 1)) * np.random.randn(*emg_clear.shape)

# Add noise to clear EMG
emg = emg_clear + emg_noise

noise_time = time.time() - noise_start_time
print(f"✓ Noise added in {noise_time:.2f}s")
print(f"✓ Final EMG shape: {emg.shape}")

## Create visualization
print("\nCreating EMG visualization...")

# Plot first 5 seconds of each channel
plot_duration = min(5.0, T / fs)  # Plot up to 5 seconds
plot_samples = int(plot_duration * fs)
timeline = np.arange(plot_samples) / fs

fig, axes = plt.subplots(
    electrode.n_channels, 1, figsize=(12, 2 * electrode.n_channels)
)
if electrode.n_channels == 1:
    axes = [axes]

fig.suptitle("Generated EMG Signals (with Contraction Profile)", fontsize=14)

for ch in range(electrode.n_channels):
    # Plot both clear and noisy signals
    axes[ch].plot(
        timeline,
        emg_clear[:plot_samples, ch],
        "b-",
        linewidth=0.8,
        alpha=0.7,
        label="Clear EMG",
    )
    axes[ch].plot(
        timeline,
        emg[:plot_samples, ch],
        "k-",
        linewidth=0.5,
        alpha=0.8,
        label="EMG + Noise",
    )

    axes[ch].set_ylabel(f"Ch {ch + 1}\n(μV)")
    axes[ch].grid(True, alpha=0.3)
    axes[ch].legend()

    # Add RMS statistics
    ch_rms_clear = np.sqrt(np.mean(emg_clear[:, ch] ** 2))
    ch_rms_noisy = np.sqrt(np.mean(emg[:, ch] ** 2))
    axes[ch].set_title(
        f"Clear RMS: {ch_rms_clear:.2f} μV, Noisy RMS: {ch_rms_noisy:.2f} μV"
    )

axes[-1].set_xlabel("Time (s)")
plt.tight_layout()
plt.savefig("emg_signals_with_profile.png", dpi=150, bbox_inches="tight")
plt.close()
print("✓ Saved EMG visualization to emg_signals_with_profile.png")

## Summary statistics
total_spikes = np.sum(spikes)
signal_duration = T / fs
spike_rate = total_spikes / (signal_duration * mu_pool.N)
active_mus = np.sum(np.any(spikes, axis=0))

# Calculate signal statistics
emg_rms = np.sqrt(np.mean(emg**2, axis=0))
emg_std = np.std(emg, axis=0)
emg_clear_rms = np.sqrt(np.mean(emg_clear**2, axis=0))

print("\n" + "=" * 60)
print("EMG signal generation completed successfully!")
print(f"✓ Signal duration: {signal_duration:.2f}s ({T} samples)")
print(f"✓ Sampling frequency: {fs} Hz")
print(f"✓ Number of channels: {electrode.n_channels}")
print(f"✓ Total spikes: {total_spikes}")
print(f"✓ Average spike rate: {spike_rate:.1f} spikes/s/MU")
print(f"✓ Active motor units: {active_mus}/{mu_pool.N}")
print(f"✓ EMG RMS (mean): {np.mean(emg_rms):.3f} μV")
print(f"✓ EMG std (mean): {np.mean(emg_std):.3f} μV")
print(f"✓ Clear EMG RMS (mean): {np.mean(emg_clear_rms):.3f} μV")
print(f"✓ Total processing time: {time.time() - start_time:.1f}s")

# Clean up intermediate variables as in MATLAB
# Note: Variables may not exist if processing was interrupted
try:
    del muap_to_add, i, t, emg_noise
except NameError:
    pass

print("\nEMG signals are now available in variables:")
print("- emg: Final EMG signals with noise")
print("- emg_clear: Clean EMG signals without noise")
print("- emg_noise: Noise component")
