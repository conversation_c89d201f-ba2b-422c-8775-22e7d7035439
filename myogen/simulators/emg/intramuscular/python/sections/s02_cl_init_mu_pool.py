"""
Glossary:
generate - generate data/parameters/entities according to a method
defined elsewhere.
init - kind of generate, but if its called the first for a new object.
calc - calculate new data as a transformation of a previously generated data.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import time

# Use non-interactive backend for server environments
matplotlib.use("Agg")

from myogen.simulators.emg.intramuscular.python.classes.MU_Pool_Sim import MU_Pool_Sim
from myogen.simulators.emg.intramuscular.python.classes.MN_Pool_Sim import MN_Pool_Sim
from myogen.simulators.emg.intramuscular.python.config import N, Rmuscle, Dmf, rr, rm

# Create motor neuron pool if not already loaded
try:
    # Try to use existing mn_pool if available
    mn_pool
except NameError:
    # Create new mn_pool if not available
    print("Creating motor neuron pool...")
    start_time = time.time()
    mn_pool = MN_Pool_Sim(N, rr, rm)
    mn_pool.distribute_innervation_centers(Rmuscle)
    print(
        f"Motor neuron pool created with {N} motor units in {time.time() - start_time:.2f}s"
    )

# Initialize the MU_Pool object with motor neuron pool
mu_pool = MU_Pool_Sim(mn_pool)
# Clear mn_pool reference as in MATLAB version
del mn_pool

print("Motor unit pool simulation initialized")

# Generate MFs
print("Generating muscle fibers...")
start_time = time.time()
mu_pool.generate_mfs(Rmuscle, Dmf)
print(f"Generated {mu_pool.Nmf} muscle fibers in {time.time() - start_time:.2f}s")

# Show MF centers
fig1, ax1 = plt.subplots(figsize=(8, 8))
mu_pool.show_mf_centers(ax1)
plt.savefig("muscle_fiber_centers.png", dpi=150, bbox_inches="tight")
plt.close()
print("Saved muscle fiber centers plot to muscle_fiber_centers.png")

# Calculate theoretical innervation areas for MNs
print("Calculating theoretical innervation areas...")
start_time = time.time()
overlap_degree = mu_pool.calc_innervation_areas()
print(
    f"Overlap degree: {overlap_degree:.3f} (calculated in {time.time() - start_time:.2f}s)"
)

# Calculate theoretical innervation numbers for MNs (number of fibers innervated by each MN)
print("Calculating theoretical innervation numbers...")
start_time = time.time()
mu_pool.calc_innervation_numbers()
total_target_fibers = np.sum(mu_pool.innervation_numbers)
print(
    f"Total target fibers: {total_target_fibers} (calculated in {time.time() - start_time:.2f}s)"
)

# Assign MFs to MNs
# Greater the parameter is, less the fibers of the same MU are allowed to be adjacent to each other.
# See the (Akhmadeev et al. 2019) paper, adjacency parameter.
print("Assigning muscle fibers to motor neurons...")
print("This may take a moment...")

# PERFORMANCE OPTIMIZATION: Choose integration method
# Options:
# 1. fast_mode=True: Skip out-of-circle compensation (fastest)
# 2. use_qmc_quad=True: Use scipy.integrate.qmc_quad (optimal for 2D, Quasi-Monte Carlo)
# 3. use_quad_vec=True: Use scipy.integrate.quad_vec (vectorized, modern)
# 4. Default: Monte Carlo integration (good balance of speed/accuracy)

USE_FAST_MODE = False  # Set to True for fastest execution
USE_QMC_QUAD = True  # Set to True for optimal 2D QMC integration
USE_QUAD_VEC = False  # Set to True to test quad_vec approach
INTEGRATION_METHOD = (
    "qmc_quad"
    if USE_QMC_QUAD
    else ("quad_vec" if USE_QUAD_VEC else ("fast" if USE_FAST_MODE else "monte_carlo"))
)

start_time = time.time()
mu_pool.assign_mfs2mns(
    4, fast_mode=USE_FAST_MODE, use_quad_vec=USE_QUAD_VEC, use_qmc_quad=USE_QMC_QUAD
)
assignment_time = time.time() - start_time
print(f"Fiber assignment complete in {assignment_time:.2f}s!")

# Show results of innervation
print("Calculating innervation results...")
start_time = time.time()
mu_pool.calc_innervation_numbers_res()

# Parameter defines the way the resulting innervation area is calculated.
# Options: 'root_variance', 'confidence_ellipse', 'polygone_area'
mu_pool.calc_innervation_areas_res("polygone_area", 0.95)
print(f"Innervation results calculated in {time.time() - start_time:.2f}s")

# Show innervation numbers (commented out in MATLAB version)
# fig2, ax2 = plt.subplots(figsize=(10, 6))
# mu_pool.show_innervation_numbers(ax2)
# plt.savefig("innervation_numbers.png", dpi=150, bbox_inches="tight")
# plt.close()

# Show innervation areas 1D (commented out in MATLAB version)
# fig3, ax3 = plt.subplots(figsize=(10, 6))
# mu_pool.show_innervation_areas_1d(ax3)
# plt.savefig("innervation_areas_1d.png", dpi=150, bbox_inches="tight")
# plt.close()

# Show innervation areas 2D (subset of motor units, every 2nd from 1 to N)
print("Creating 2D innervation areas visualization...")
start_time = time.time()
fig4, ax4 = plt.subplots(figsize=(10, 10))
# Convert MATLAB 1:2:N indexing to Python (0-based, step=2)
mu_indices = np.arange(0, N, 1)  # Python equivalent of MATLAB 1:2:N
mu_pool.show_innervation_areas_2d(mu_indices, ax4)
ax4.set_title("")  # Remove title as in MATLAB version
plt.savefig("innervation_areas_2d.png", dpi=150, bbox_inches="tight")
plt.close()
print(
    f"Saved 2D innervation areas plot to innervation_areas_2d.png in {time.time() - start_time:.2f}s"
)

# Generate diameters and conduction velocities
print("Generating muscle fiber diameters and conduction velocities...")
start_time = time.time()
mu_pool.generate_mf_diameters()
mu_pool.generate_mf_cvs()
print(f"Fiber properties generated in {time.time() - start_time:.2f}s")

# Show diameters distribution (commented out in MATLAB version)
# fig5, ax5 = plt.subplots(figsize=(8, 6))
# mu_pool.show_diameters_distribution(ax5)
# plt.savefig("diameters_distribution.png", dpi=150, bbox_inches="tight")
# plt.close()

# Clean up variables as in MATLAB version
del Dmf
# Nmf is not defined in this script in MATLAB version, so we don't delete it

print("\nMotor unit pool simulation completed successfully!")
print(f"Number of motor units: {mu_pool.N}")
print(f"Number of muscle fibers: {mu_pool.Nmf}")
print(f"Muscle radius: {Rmuscle:.2f} mm")
print(f"Fiber density: {mu_pool.Dmf} fibers/mm²")
print(f"Overlap degree: {overlap_degree:.3f}")
print(f"Integration method: {INTEGRATION_METHOD}")
print(f"Total fiber assignment time: {assignment_time:.2f}s")

if USE_FAST_MODE:
    print("\nNOTE: Fast mode was used for fiber assignment.")
    print(
        "For maximum accuracy, set USE_FAST_MODE = False (but expect longer execution time)."
    )
elif USE_QMC_QUAD:
    print("\nNOTE: qmc_quad integration was used for fiber assignment.")
    print("This uses scipy's optimal Quasi-Monte Carlo integration for 2D.")
elif USE_QUAD_VEC:
    print("\nNOTE: quad_vec integration was used for fiber assignment.")
    print("This uses scipy's modern vectorized integration with polar coordinates.")
else:
    print("\nNOTE: Monte Carlo integration was used for fiber assignment.")
    print("This provides a good balance of speed and accuracy.")
