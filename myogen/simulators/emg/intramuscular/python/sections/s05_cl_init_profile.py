"""
Script s05_cl_init_profile.py
Initialize contraction profile for EMG intramuscular simulation.

This script:
1. Generates a trapezoidal contraction profile
2. Sets up electrode trajectory parameters for motion simulation
3. Displays the generated profile

Translated from MATLAB script s05_cl_init_profile.m
"""

import numpy as np
from myogen.simulators.emg.intramuscular.python.config import fs
from myogen.simulators.emg.intramuscular.python.classes.ContractionProfile import (
    ContractionProfile,
)

print("Initializing contraction profile...")
print("=" * 50)

## Generate trapezoidal profile
print("Generating trapezoidal contraction profile...")

# Profile parameters (matching MATLAB script)
profile_len = 25  # simulation time seconds
silence = 1  # silence period at start/end
percent_mvc = 100  # percent MVC
slope = 10  # percent per second

# Create trapezoidal profile
profile = ContractionProfile(
    profile_len=profile_len,
    fs=fs,
    profile_type="trapezoidal",
    percent_mvc=percent_mvc / 100,  # Convert percentage to fraction
    slope=slope / 100,  # Convert percentage to fraction
    silence=silence,
)

print(f"✓ Trapezoidal profile created:")
print(f"  - Duration: {profile_len} seconds")
print(f"  - Peak MVC: {percent_mvc}%")
print(f"  - Slope: {slope}%/second")
print(f"  - Silence periods: {silence} seconds")
print(f"  - Total samples: {profile.T}")

# Alternative profile types (commented out, as in MATLAB)
# profile = ContractionProfile(profile_len, fs, 'constant', percent_mvc/100)
# profile = ContractionProfile(profile_len, fs, 'ramp', percent_mvc/100, slope/100, silence)
# profile = ContractionProfile(profile_len, fs, 'rect', percent_mvc/100)

## Show generated profile
print("\nDisplaying profile...")
profile.show(save_path="contraction_profile.png")

# Clean up intermediate variables (matching MATLAB script)
# In Python, we can use del to remove variables, but it's generally not necessary
# del profile_len, percent_mvc, slope

print("✓ Profile visualization saved as 'contraction_profile.png'")

## Electrode shifts
print("\nSetting up electrode trajectory parameters...")

# Time-dependent translation of the electrode
traj_parameter_map = (np.arange(1, profile.T + 1) / profile.T).reshape(-1, 1)
traj_parameter_map_type = "Time-dependent"

print(f"✓ Trajectory parameter map created:")
print(f"  - Type: {traj_parameter_map_type}")
print(f"  - Shape: {traj_parameter_map.shape}")
print(f"  - Range: [{traj_parameter_map.min():.3f}, {traj_parameter_map.max():.3f}]")

# Alternative: No motion (commented out, as in MATLAB)
# traj_parameter_map = np.zeros((profile.T, 1))
# traj_parameter_map_type = 'No motion'

print("\n" + "=" * 50)
print("Contraction profile initialization completed!")
print("\nProfile object 'profile' is now available with the following properties:")
print(f"  - profile.profile: contraction signal ({len(profile.profile)} samples)")
print(f"  - profile.T: total time samples ({profile.T})")
print(f"  - profile.profile_type: '{profile.profile_type}'")
print(f"  - profile.fs: {profile.fs} Hz")
print("\nTrajectory parameters:")
print(f"  - traj_parameter_map: {traj_parameter_map.shape} array")
print(f"  - traj_parameter_map_type: '{traj_parameter_map_type}'")
