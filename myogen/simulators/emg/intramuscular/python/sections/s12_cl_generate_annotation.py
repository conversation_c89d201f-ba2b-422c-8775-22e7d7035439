"""
Script 12: Generate annotation for EMG signals
Translates spikes to action potential centered annotations for analysis.
"""

import numpy as np
import matplotlib.pyplot as plt


def find_ap_center_gauss(muap):
    """
    Find the center of action potential using Gaussian fitting approach.
    This is a placeholder - implement based on your specific requirements.
    """
    if muap.ndim == 1:
        # Single dimension case - just find the peak
        return np.argmax(np.abs(muap))
    elif muap.ndim == 2:
        # Multi-channel case
        if muap.shape[1] == 1:
            # Single channel, multiple time points
            return np.argmax(np.abs(muap[:, 0]))
        else:
            # Multiple channels - find the channel with maximum peak-to-peak amplitude
            pp_amplitudes = np.max(muap, axis=0) - np.min(muap, axis=0)
            center_channel = np.argmax(pp_amplitudes)

            # Find the peak location in that channel
            peak_idx = np.argmax(np.abs(muap[:, center_channel]))
            return peak_idx
    else:
        raise ValueError(f"Unexpected MUAP dimensions: {muap.shape}")


def spikes2firings(spikes):
    """
    Convert spike trains to firing times.
    """
    if spikes.ndim == 2:
        # 2D case: (time, motor_units)
        firings = []
        for mu_idx in range(spikes.shape[1]):
            firing_times = np.where(spikes[:, mu_idx] > 0)[0]
            firings.append(firing_times)
        return firings
    elif spikes.ndim == 3:
        # 3D case: (time, motor_units, channels)
        firings = []
        for ch_idx in range(spikes.shape[2]):
            ch_firings = []
            for mu_idx in range(spikes.shape[1]):
                firing_times = np.where(spikes[:, mu_idx, ch_idx] > 0)[0]
                ch_firings.append(firing_times)
            firings.append(ch_firings)
        return firings


print("Generating annotations...")

# Generate full causal annotation (commented out in original MATLAB)
# Problem: original spikes correspond to neuron's spikes, while decomposed spikes
# correspond to center of action potential. Decomposed spikes are around +5ms
# compared to neuron's spikes due to axon and muscle fiber conduction velocities.

# annotation_delays = np.zeros(mu_pool.N)  # In samples!
# for m in range(mu_pool.N):
#     mf_conduction_delay = round(fs * np.abs(np.mean((MUs[m].nmj_z - np.mean(electrode_pts[:, 2])) / MUs[m].mf_cv)))
#     axon_delay = round(fs * np.mean(MUs[m].mnap_delays))
#     annotation_delays[m] = mf_conduction_delay + axon_delay

# annotation_spikes_full = np.full_like(spikes, np.nan)
# for m in range(mu_pool.N):
#     annotation_spikes_full[:, m] = shift_padding(spikes[:, m], annotation_delays[m], 1)
# annotation_firings_full = spikes2firings(annotation_spikes_full)

# Generate causal annotation for detectable MUs (commented out in original)
# annotation_spikes_detectable = np.full((spikes.shape[0], len(detectable_ind)), np.nan)
# for m in range(len(detectable_ind)):
#     annotation_spikes_detectable[:, m] = shift_padding(spikes[:, detectable_ind[m]], annotation_delays[detectable_ind[m]], 1)
# annotation_firings_detectable = spikes2firings(annotation_spikes_detectable)

# Generate full AP-centered annotation
print("Computing AP centers for all motor units...")
ap_centers_full = np.zeros((mu_pool.N, electrode.n_channels), dtype=int)

for m in range(mu_pool.N):
    # Get trajectory mixing matrix
    temp_traj = electrode.traj_mixing_mat(0, electrode.n_nodes, electrode.n_channels)

    # Debug dimensions
    print(
        f"MU {m}: MUAP shape: {MUs[m].muap.shape}, traj shape: {temp_traj.shape}, diff_mat shape: {electrode.diff_mat.shape}"
    )

    # Calculate MUAP at electrode and find AP center
    # Check dimensions before matrix multiplication
    if MUs[m].muap.shape[1] != temp_traj.shape[0]:
        print(
            f"Warning: Dimension mismatch for MU {m}. MUAP cols: {MUs[m].muap.shape[1]}, traj rows: {temp_traj.shape[0]}"
        )
        # Skip this MU for now to avoid error
        continue

    muap_at_electrode = MUs[m].muap @ temp_traj.T @ electrode.diff_mat.T

    # Check if single channel - need to handle different cases
    if electrode.n_channels == 1:
        ap_centers_full[m, 0] = find_ap_center_gauss(muap_at_electrode.flatten())
    else:
        # For multi-channel, use the transposed matrix
        ap_centers_full[m, :] = find_ap_center_gauss(muap_at_electrode.T)

print("Generating centered spikes for all motor units...")
spikes_centered_full = np.zeros(
    (spikes.shape[0], spikes.shape[1], electrode.n_channels)
)

for m in range(mu_pool.N):
    for ch in range(electrode.n_channels):
        center = ap_centers_full[m, ch]
        if center > 0:
            # Create centered spikes by shifting
            spikes_centered_full[:, m, ch] = np.concatenate(
                [np.zeros(center - 1), spikes[: len(spikes) - center + 1, m]]
            )

# Convert to firings
firings_centered_full = spikes2firings(spikes_centered_full)

# Generate AP-centered annotation for detectable motor units
print("Generating centered spikes for detectable motor units...")
spikes_centered_detectable = spikes_centered_full[:, detectable_ind, :]
firings_centered_detectable = spikes2firings(spikes_centered_detectable)

print(f"Generated annotations for {len(detectable_ind)} detectable motor units")
print(f"AP centers shape: {ap_centers_full.shape}")
print(f"Centered spikes shape: {spikes_centered_full.shape}")

# Plotting EMG with spikes (optional - can be enabled if needed)
plot_emg_with_annotations = False  # Set to True to enable plotting

if plot_emg_with_annotations:
    print("Plotting EMG with annotations...")

    fig, ax = plt.subplots(figsize=(13, 6))

    # Calculate separators for multichannel view
    separator_step = np.mean(np.std(emg, axis=0)) * 5
    separator = np.arange(1, electrode.n_channels + 1) * separator_step
    separator = np.tile(separator, (emg.shape[0], 1))

    # Plot EMG (with separator for multichannel view)
    ax.plot(profile.timeline, emg + separator, linewidth=1.1, label="Signal")

    # Plot centered firings
    centered_sum = np.sum(spikes_centered_detectable, axis=1)
    ax.plot(
        profile.timeline,
        centered_sum * np.std(emg) + separator,
        linewidth=1.2,
        label="Centered Firings",
    )

    ax.set_title(f"Simulated EMG, [V], SNR={SNR:.2f}")
    ax.set_xlabel("Time, s")
    ax.set_ylabel("Amplitude, arbitrary units")
    ax.legend()

    plt.tight_layout()
    plt.savefig("emg_signals_with_annotations.png", dpi=300, bbox_inches="tight")
    plt.show()

print("✓ Annotation generation completed")
