"""
Script 15: Reconstruct EMG signal from spike trains and MUAPs
Translates s15_cl_reconstruct_signal.m from MATLAB to Python
"""

import numpy as np
import matplotlib.pyplot as plt

print("Script 15: Reconstructing EMG signal...")

# Initialize reconstruction arrays - exactly like MATLAB
temp_reconstructed_emg_full = np.zeros(
    (spikes.shape[0], electrode.n_points * electrode.n_nodes)
)
temp_reconstructed_emg_detectable = np.zeros(
    (spikes.shape[0], electrode.n_points * electrode.n_nodes)
)

# Loop through motor units and electrode points - exactly like MATLAB
for mu in range(mu_pool.N):
    for pt in range(electrode.n_points * electrode.n_nodes):
        # Convolve spike train with MUAP - exactly like MATLAB conv(..., 'full')
        temp_train = np.convolve(spikes[:, mu], MUs[mu].muap[:, pt], mode="full")

        # MATLAB: temp_train(1: end-size(MUs(mu).muap, 1)+1)
        # This trims the convolution to remove the padding at the end
        muap_length = MUs[mu].muap.shape[0]
        trimmed_train = temp_train[: len(temp_train) - muap_length + 1]

        # Add to full reconstruction
        temp_reconstructed_emg_full[:, pt] += trimmed_train

        # Add to detectable reconstruction if this MU is detectable
        # MATLAB: if any(mu == detectable_ind)
        if np.any(mu == detectable_ind):
            temp_reconstructed_emg_detectable[:, pt] += trimmed_train

# Initialize final reconstruction arrays - exactly like MATLAB
reconstructed_emg_full = np.zeros(
    (temp_reconstructed_emg_full.shape[0], electrode.n_channels)
)
reconstructed_emg_detectable = np.zeros(
    (temp_reconstructed_emg_detectable.shape[0], electrode.n_channels)
)

# Apply trajectory mixing and differential matrices - exactly like MATLAB
traj_parameter_map = force  # MATLAB: traj_parameter_map = force;
for i in range(T):  # MATLAB: for i = 1:T
    # Get trajectory mixing matrix - exactly like MATLAB
    temp_traj = electrode.traj_mixing_mat(
        traj_parameter_map[i], electrode.n_nodes, electrode.n_channels
    )

    # Apply trajectory mixing and differential matrix - exactly like MATLAB
    # MATLAB: reconstructed_emg_full(i,:) = temp_reconstructed_emg_full(i,:) * temp_traj' * electrode.diff_mat';
    reconstructed_emg_full[i, :] = (
        temp_reconstructed_emg_full[i, :] @ temp_traj.T @ electrode.diff_mat.T
    )
    reconstructed_emg_detectable[i, :] = (
        temp_reconstructed_emg_detectable[i, :] @ temp_traj.T @ electrode.diff_mat.T
    )


# Optional plotting function (commented out in MATLAB)
def plot_reconstructed_signals():
    """
    Plot reconstructed vs original EMG signals.
    This implements the commented plotting code from MATLAB.
    """
    fig = plt.figure(figsize=(13, 6))

    # Calculate separator for multichannel view
    separator_step = np.mean(np.std(reconstructed_emg_full, axis=0)) * 5
    separator = np.arange(1, electrode.n_channels + 1) * separator_step
    separator = np.tile(separator, (reconstructed_emg_full.shape[0], 1))

    # Plot EMG with detectable reconstruction
    plt.subplot(2, 1, 1)
    plt.plot(
        profile.timeline, emg + separator, "k", linewidth=1, label="Original signal"
    )
    plt.plot(
        profile.timeline,
        reconstructed_emg_detectable + separator,
        "b",
        linewidth=1,
        label="Reconstr. from detectable dict.",
    )
    plt.legend()

    # Plot EMG with full reconstruction
    plt.subplot(2, 1, 2)
    plt.plot(
        profile.timeline, emg + separator, "k", linewidth=1, label="Original signal"
    )
    plt.plot(
        profile.timeline,
        reconstructed_emg_full + separator,
        "g",
        linewidth=1,
        label="Reconstr. from full dict.",
    )
    plt.legend()
    plt.title("Reconstructed EMG vs Simulated")
    plt.xlabel("Time, s")
    plt.ylabel("Amplitude, Arbitrary units")

    plt.tight_layout()
    plt.show()


# Uncomment to show plot (equivalent to uncommenting MATLAB figure code)
# plot_reconstructed_signals()

# Clean up temporary variables - exactly like MATLAB clear command
# MATLAB: clear temp* reconstructed_* separator* mu
temp_vars_to_clear = [name for name in locals() if name.startswith("temp_")]
for var_name in temp_vars_to_clear:
    if var_name in locals():
        del locals()[var_name]

print("✓ EMG signal reconstruction completed")
print(f"  - Full reconstruction shape: {reconstructed_emg_full.shape}")
print(f"  - Detectable reconstruction shape: {reconstructed_emg_detectable.shape}")
