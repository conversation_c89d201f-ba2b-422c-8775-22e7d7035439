"""
Script s06_cl_generate_force.py
Generate force according to the specified profile. Simulates the
contraction and outputs the spike trains for this contraction.

Translated from MATLAB script s06_cl_generate_force.m
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib

# Use non-interactive backend for server environments
matplotlib.use("Agg")

from myogen.simulators.emg.intramuscular.python.config import fs, fsl


def check_dependencies():
    """Check if required objects exist from previous scripts."""
    missing_deps = []

    try:
        profile
    except NameError:
        missing_deps.append("profile (run s05_cl_init_profile.py)")

    try:
        mu_pool
    except NameError:
        missing_deps.append("mu_pool (run s02_cl_init_mu_pool.py)")

    try:
        mf_mdl
    except NameError:
        missing_deps.append("mf_mdl (run s03_cl_init_force_model.py)")

    try:
        pid_params
    except NameError:
        missing_deps.append("pid_params (run s04_cl_tune_pid.py)")

    if missing_deps:
        raise NameError(f"Missing dependencies: {', '.join(missing_deps)}")

    return True


# Check dependencies
check_dependencies()

print("Generating force according to profile...")
print("=" * 50)

# Initialize arrays
spikes = np.zeros((profile.T, mu_pool.N))
ipi = np.zeros((profile.T, mu_pool.N))
force = np.zeros(profile.T)
excitation = np.zeros(profile.T)

error = np.zeros(profile.T)
int_err = 0
der_err = 0

# PID parameters
Kp = pid_params["Kp"]
Ki = pid_params["Ki"] * (1.0 / fsl)  # Convert Ki to discrete-time (Ts = 1/fsl)
Kd = pid_params["Kd"]
Ts = 1.0 / fsl  # Sampling time for control loop

print(f"PID Parameters:")
print(f"  Kp = {Kp:.6f}")
print(f"  Ki = {Ki:.6f} (discrete-time)")
print(f"  Kd = {Kd:.6f}")
print(f"  Ts = {Ts:.6f} s")

prev_neuron_state = np.full(mu_pool.N, np.nan)

sub_counter = 0

# Subsample the profile for control
profile.subsample(fsl)
sub_error = np.zeros(len(profile.sub_profile))
sub_force = np.zeros(len(profile.sub_profile))
sub_exc = np.zeros(len(profile.sub_profile))
sub_t = 0

# Initialize online buffer for force model
mf_mdl.init_online_buffer()

print(f"Starting force generation loop...")
print(f"Profile length: {profile.T} samples ({profile.T / fs:.1f} seconds)")
print(f"Subsampled profile length: {len(profile.sub_profile)} samples")

# Main force generation loop
for t in range(profile.T - 1):
    # Error accumulation for subsampling
    error[t] = profile.profile[t] - force[t]

    sub_counter += 1

    # Check if we need to update the control signal (subsampling)
    if sub_counter % round(fs / fsl) == 0:
        # Error calculation for PID controller's input
        start_idx = max(0, t - round(fs / fsl) + 1)
        sub_force[sub_t] = np.mean(force[start_idx : t + 1])
        sub_error[sub_t] = (
            profile.sub_profile[sub_t] - sub_force[sub_t]
        )  # Proportional error

        # Integrated error (suppressed when no goal force)
        if profile.sub_profile[sub_t] > 0:
            int_err += sub_error[sub_t]
        else:
            int_err = 0

        # Local derivative of the error
        if sub_t > 0:
            der_err = sub_error[sub_t] - sub_error[sub_t - 1]

        # PID controller's output is net excitation to the motor neuron pool
        sub_exc[sub_t] = Kp * sub_error[sub_t] + Ki * int_err + Kd * der_err
        sub_exc[sub_t] = max(sub_exc[sub_t], 0)
        sub_exc[sub_t] = min(sub_exc[sub_t], 1)

        # The inverse model
        if profile.sub_profile[sub_t] <= 0:
            sub_exc[sub_t] = 0
        elif profile.sub_profile[sub_t] >= 1:
            sub_exc[sub_t] = 1
        else:
            sub_exc[sub_t] = sub_exc[sub_t] + mf_mdl.f2e(profile.sub_profile[sub_t])

        sub_t += 1

    # Upsampling back...
    if sub_t == 0:
        excitation[t] = 0
    else:
        excitation[t] = sub_exc[sub_t - 1]

    # Force generation
    spikes_t, prev_neuron_state, ipi_t = mu_pool.mn_pool.generate_spike_train_gauss(
        np.array([t + 1]), prev_neuron_state, np.array([excitation[t]]), fs
    )
    spikes[t, :] = spikes_t[0, :]  # Extract first (and only) row
    ipi[t, :] = ipi_t
    force[t + 1] = mf_mdl.generate_force_online(spikes_t[0, :], ipi_t)

    # Progress indicator
    if (t + 1) % fs == 0:
        print(f"{(t + 1) // fs} seconds generated")

print("✓ Force generation completed!")

# Calculate some statistics
max_force = np.max(force)
max_error = np.max(np.abs(error))
rmse_error = np.sqrt(np.mean(error**2))

print(f"\nForce Generation Statistics:")
print(f"  Maximum force achieved: {max_force:.4f}")
print(f"  Maximum absolute error: {max_error:.4f}")
print(f"  RMS error: {rmse_error:.4f}")
print(f"  Total spikes generated: {np.sum(spikes)}")

## Optional: Plot goal force + resulting force, excitation and error
print("\nGenerating force comparison plot...")
timeline = np.arange(profile.T) / fs

fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))

# Force comparison
ax1.plot(timeline, force * 100, "g", linewidth=1.5, label="Resulting force")
ax1.plot(timeline, error * 100, "r", linewidth=1, label="Error")
ax1.plot(timeline, profile.profile * 100, "b--", linewidth=1, label="Goal force")
ax1.set_ylabel("Force, %MVC")
ax1.set_xlabel("Time, s")
ax1.legend()
ax1.grid(True, alpha=0.3)
ax1.set_title("Force Control Performance")

# Excitation signal
ax2.plot(timeline, excitation, "purple", linewidth=1.5, label="Excitation")
ax2.set_ylabel("Excitation, normalized units")
ax2.set_xlabel("Time, s")
ax2.legend()
ax2.grid(True, alpha=0.3)
ax2.set_title("Motor Pool Excitation Signal")

plt.tight_layout()
plt.savefig("force_generation_results.png", dpi=150, bbox_inches="tight")
plt.close()
print("✓ Force comparison plot saved as 'force_generation_results.png'")

## Optional: Plot resulting spikes
print("Generating spike raster plot...")


def spikes2firings(spikes_matrix):
    """Convert spike matrix to firing times list."""
    firings = []
    for mu_idx in range(spikes_matrix.shape[1]):
        spike_times = np.where(spikes_matrix[:, mu_idx] > 0)[0]
        firings.append(spike_times)
    return firings


firings = spikes2firings(spikes)

fig, ax = plt.subplots(figsize=(12, 8))

# Plot spike raster
for m, spike_times in enumerate(firings):
    if len(spike_times) > 0:
        indices = spike_times / fs
        separator = np.full(len(indices), m + 1)
        ax.plot(indices, separator, "ko", markersize=1, alpha=0.7)

ax.set_ylabel("Motor neuron index")
ax.set_xlabel("Time, sec")
ax.set_title("Motor neurons firing times")

# Add excitation on secondary y-axis
ax2 = ax.twinx()
ax2.plot(timeline, excitation, "purple", linewidth=2, alpha=0.8)
ax2.set_ylabel("Excitation, normalized units", color="purple")
ax2.tick_params(axis="y", labelcolor="purple")

plt.tight_layout()
plt.savefig("spike_raster_plot.png", dpi=150, bbox_inches="tight")
plt.close()
print("✓ Spike raster plot saved as 'spike_raster_plot.png'")

print("\n" + "=" * 50)
print("Force generation completed successfully!")
print("\nGenerated data:")
print(f"  - spikes: {spikes.shape} array of spike trains")
print(f"  - ipi: {ipi.shape} array of inter-pulse intervals")
print(f"  - force: {force.shape} array of resulting force")
print(f"  - excitation: {excitation.shape} array of excitation signal")
print(f"  - error: {error.shape} array of control error")

# Clean up intermediate variables (matching MATLAB script)
del sub_counter, sub_error, sub_force, sub_exc, sub_t
del Kp, Ki, Kd, Ts, int_err, der_err, prev_neuron_state
del timeline, max_force, max_error, rmse_error, firings
