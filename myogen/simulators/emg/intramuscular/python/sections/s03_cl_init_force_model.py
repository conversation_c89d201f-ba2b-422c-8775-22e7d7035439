"""
Initialize muscle force model and compute force responses.
This script translates the MATLAB version s03_cl_init_force_model.m to Python.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import time

# Use non-interactive backend for server environments
matplotlib.use("Agg")

from myogen.simulators.emg.intramuscular.python.classes.MuscleForceMdl import (
    MuscleForceMdl,
)
from myogen.simulators.emg.intramuscular.python.config import fs

# Ensure mu_pool exists from previous script
try:
    mu_pool
except NameError:
    raise NameError(
        "mu_pool not found. Please run s02_cl_init_mu_pool.py first to create the motor unit pool."
    )

print("Initializing muscle force model...")

# Create a muscle model object, generate twitches according to Fuglevand 1993
start_time = time.time()
mf_mdl = MuscleForceMdl(mu_pool.mn_pool.N, mu_pool.mn_pool.rr, fs)
print(f"Muscle force model created in {time.time() - start_time:.2f}s")

# Plot twitches
print("Plotting twitch waveforms...")
fig_twitches = mf_mdl.plot_twitches()
plt.savefig("muscle_twitches.png", dpi=150, bbox_inches="tight")
plt.close()
print("Saved twitch waveforms to muscle_twitches.png")

# Estimate the maximum voluntary contraction, normalize force
print("Estimating maximum voluntary contraction (MVC)...")
start_time = time.time()

# Define MVC spikes
mvc_T = int(10 * fs)  # Length of the MVC measurement signal
mvc_excitation = np.ones(mvc_T)
mvc_spikes, _, _ = mu_pool.mn_pool.generate_spike_train_gauss(
    np.arange(1, mvc_T + 1), np.full(mu_pool.mn_pool.N, np.nan), mvc_excitation, fs
)
mvc_force, fmax = mf_mdl.normalize_mvc(mvc_spikes)

print(f"MVC estimation completed in {time.time() - start_time:.2f}s")
print(f"Maximum force value (fmax): {fmax:.6f}")

# Optional: Plot MVC after normalization (commented as in MATLAB)
# fig_mvc, ax_mvc = plt.subplots(figsize=(10, 6))
# timeline_mvc = np.arange(mvc_T) / fs
# ax_mvc.plot(timeline_mvc, mvc_force)
# ax_mvc.set_title('MVC after normalization')
# ax_mvc.set_ylabel('Force, normalized')
# ax_mvc.set_xlabel('Time, s')
# plt.savefig("mvc_normalized.png", dpi=150, bbox_inches="tight")
# plt.close()

# Clean up MVC variables as in MATLAB
del mvc_T, mvc_excitation, mvc_spikes, mvc_force

# Get the quasistatic inverse model of the muscle
print("Computing quasistatic inverse model...")
start_time = time.time()

qsi_T = int(25 * fs)
qsi_excitation = np.linspace(0, 1, qsi_T)
qsi_spikes, _, _ = mu_pool.mn_pool.generate_spike_train_gauss(
    np.arange(1, qsi_T + 1), np.full(mu_pool.mn_pool.N, np.nan), qsi_excitation, fs
)
qsi_force = mf_mdl.generate_force_offline(qsi_spikes, "QSI measurement:")

print(f"Quasistatic model computation completed in {time.time() - start_time:.2f}s")

# Initialize quasistatic excitation-to-force and force-to-excitation models
print("Initializing quasistatic models...")
start_time = time.time()
mf_mdl.init_quasistatic_e2f_f2e_models(mu_pool)
print(f"Quasistatic models initialized in {time.time() - start_time:.2f}s")

# Optional: Plot force-excitation relationship (commented as in MATLAB)
# fig_qsi, ax_qsi = plt.subplots(figsize=(10, 6))
# ax_qsi.plot(qsi_force, qsi_excitation, 'r', linewidth=2, label='Simulated excitation-force dependency')
# ax_qsi.plot(qsi_force, mf_mdl.f2e(qsi_force), 'b', linewidth=2, label='Weighted 5-th order polynomial fit')
# ax_qsi.set_xlim([0, 1])
# ax_qsi.set_ylim([0, 1])
# ax_qsi.set_ylabel('Excitation')
# ax_qsi.set_xlabel('Force, normalized')
# ax_qsi.legend(loc='upper left')
# plt.savefig("quasistatic_model.png", dpi=150, bbox_inches="tight")
# plt.close()

# Clean up quasistatic variables as in MATLAB
del qsi_force, qsi_excitation, qsi_T, qsi_spikes

print("\nMuscle force model initialization completed successfully!")
print(f"Number of motor units: {mf_mdl.N}")
print(f"Sampling frequency: {mf_mdl.hfs} Hz")
print(f"Maximum force (fmax): {mf_mdl.fmax:.6f}")
print("Quasistatic excitation-to-force and force-to-excitation models are ready.")
