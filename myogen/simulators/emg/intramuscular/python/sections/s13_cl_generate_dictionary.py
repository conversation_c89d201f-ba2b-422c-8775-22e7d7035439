"""
Script 13: Generate Dictionary
Generates MUAP dictionaries for both full and detectable motor units across trajectory nodes.
"""

import numpy as np

print("Generating MUAP dictionaries...")

# Generate full dictionary
# MUAPs matrices are (samples x channel x motor_unit)
max_muap_len = 0
for m in range(mu_pool.N):
    if MUs[m].muap.shape[0] > max_muap_len:
        max_muap_len = MUs[m].muap.shape[0]

# Full dictionary of forms in trajectory nodes
dictionary_full_traj = []
for k in range(electrode.n_nodes):
    dictionary_full_traj.append(
        np.zeros((max_muap_len, electrode.n_channels, mu_pool.N))
    )
    for m in range(mu_pool.N):
        # Pad MUAP to max length
        temp_muap = np.vstack(
            [
                MUs[m].muap,
                np.zeros((max_muap_len - MUs[m].muap.shape[0], MUs[m].muap.shape[1])),
            ]
        )

        # Get trajectory mixing matrix
        temp_traj = electrode.traj_mixing_mat(
            k / (electrode.n_nodes - 1 + np.finfo(float).eps),
            electrode.n_nodes,
            electrode.n_channels,
        )

        # Apply trajectory and differential matrices
        dictionary_full_traj[k][:, :, m] = (
            temp_muap @ temp_traj.T @ electrode.diff_mat.T
        )

# Full dictionary of initial forms (MUAPs in the initial electrode position)
dictionary_full_init = dictionary_full_traj[0]

# Generate indices of MUAPs actually present in the signal (active motor units)
largest_active_full_mu = None
spike_activity = np.any(spikes, axis=0)  # Check if any spikes exist for each MU
if np.any(spike_activity):
    largest_active_full_mu = np.where(spike_activity)[0][-1]

print(f"Maximum MUAP length: {max_muap_len} samples")
print(f"Full dictionary shape: {dictionary_full_init.shape}")
if largest_active_full_mu is not None:
    print(f"Largest active full MU: {largest_active_full_mu}")

# Generate detectable dictionary
# Full dictionary of forms in trajectory nodes
dictionary_detectable_traj = []
for k in range(electrode.n_nodes):
    dictionary_detectable_traj.append(
        np.zeros((max_muap_len, electrode.n_channels, len(detectable_ind)))
    )
    for m in range(len(detectable_ind)):
        temp_ind = detectable_ind[m]

        # Pad MUAP to max length
        temp_muap = np.vstack(
            [
                MUs[temp_ind].muap,
                np.zeros(
                    (
                        max_muap_len - MUs[temp_ind].muap.shape[0],
                        MUs[temp_ind].muap.shape[1],
                    )
                ),
            ]
        )

        # Get trajectory mixing matrix
        temp_traj = electrode.traj_mixing_mat(
            k / (electrode.n_nodes - 1 + np.finfo(float).eps),
            electrode.n_nodes,
            electrode.n_channels,
        )

        # Apply trajectory and differential matrices
        dictionary_detectable_traj[k][:, :, m] = (
            temp_muap @ temp_traj.T @ electrode.diff_mat.T
        )

# Detectable dictionary of initial forms (MUAPs in the initial electrode position)
dictionary_detectable_init = dictionary_detectable_traj[0]

# Find largest active detectable motor unit
largest_active_detectable_mu = None
if largest_active_full_mu is not None:
    active_detectable = detectable_ind <= largest_active_full_mu
    if np.any(active_detectable):
        largest_active_detectable_mu = np.where(active_detectable)[0][-1]

# Comments for dictionary structures
comment_full_init = "dictionary_full_init(t, channel, motor unit)"
comment_full_traj = "dictionary_full_traj[trajectory node](t, channel, motor unit)"
comment_detectable_init = "dictionary_detectable_init(t, channel, motor unit)"
comment_detectable_traj = (
    "dictionary_detectable_traj[trajectory node](t, channel, motor unit)"
)

print(f"Detectable dictionary shape: {dictionary_detectable_init.shape}")
if largest_active_detectable_mu is not None:
    print(f"Largest active detectable MU: {largest_active_detectable_mu}")

# In case of single channel electrode, automatically squeeze the dictionaries
if electrode.n_channels == 1:
    print("Single channel electrode detected - squeezing channel dimension...")
    dictionary_detectable_init = np.squeeze(dictionary_detectable_init, axis=1)
    dictionary_full_init = np.squeeze(dictionary_full_init, axis=1)

    for i in range(len(dictionary_detectable_traj)):
        dictionary_detectable_traj[i] = np.squeeze(
            dictionary_detectable_traj[i], axis=1
        )

    for i in range(len(dictionary_full_traj)):
        dictionary_full_traj[i] = np.squeeze(dictionary_full_traj[i], axis=1)

print("Dictionary generation completed successfully!")
print(f"Full dictionary: {len(dictionary_full_traj)} trajectory nodes")
print(f"Detectable dictionary: {len(dictionary_detectable_traj)} trajectory nodes")
print(f"Number of detectable MUs: {len(detectable_ind)}")
