"""
Script s09_cl_generate_mvc_emg.py
Generate Maximum Voluntary Contraction (MVC) EMG signals for intramuscular simulation.

This script:
1. Generates MVC spike trains using the motor neuron pool
2. Constructs MVC EMG by summing MUAPs at spike times
3. Applies electrode trajectory mixing and differential recording
4. Calculates noise parameters based on SNR

Translated from MATLAB script s09_cl_generate_mvc_emg.m
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import time

# Use non-interactive backend for server environments
matplotlib.use("Agg")

from myogen.simulators.emg.intramuscular.python.config import fs

# Ensure required variables exist from previous scripts
try:
    mu_pool
    electrode
    MUs
    max_muap_len
except NameError:
    raise NameError(
        "Required variables not found. Please run previous scripts in sequence:\n"
        "- s01_cl_init_mn_pool.py\n"
        "- s02_cl_init_mu_pool.py\n"
        "- s03_cl_init_force_model.py\n"
        "- s04_cl_tune_pid.py\n"
        "- s07_cl_init_electrode.py\n"
        "- s08_cl_init_muaps.py"
    )

print("Generating Maximum Voluntary Contraction (MVC) EMG signals...")
print("=" * 60)

## Generate MVC spikes
print("Generating MVC spike trains...")
start_time = time.time()

# Define MVC duration (5 seconds)
mvc_duration = 5  # seconds
mvc_T = int(mvc_duration * fs)

# Generate MVC spike trains with maximum excitation (ones)
mvc_spikes, _, _ = mu_pool.mn_pool.generate_spike_train_gauss(
    np.arange(1, mvc_T + 1),  # Time vector
    np.full(mu_pool.mn_pool.N, np.nan),  # Previous state (all NaN)
    np.ones(mvc_T),  # Maximum excitation
    fs,  # Sampling frequency
)

print(f"✓ MVC spike trains generated in {time.time() - start_time:.2f}s")
print(f"Signal duration: {mvc_duration}s ({mvc_T} samples)")
print(f"Spike matrix shape: {mvc_spikes.shape}")

## Generate MVC EMG
print("\nGenerating MVC EMG signals...")
start_time = time.time()

# Initialize EMG signal array
# Shape: (time_samples, electrode_points * electrode_nodes) - match MATLAB
mvc_emg = np.zeros((mvc_spikes.shape[0], electrode.n_points * electrode.n_nodes))

# Generate EMG by summing MUAPs at spike times - follow MATLAB loop structure
print("Processing motor units:")
for i in range(mu_pool.N):
    mu_start_time = time.time()
    spike_count = 0

    # Loop through time samples as in MATLAB
    for t in range(mvc_spikes.shape[0] - max_muap_len - 1):
        if mvc_spikes[t, i]:
            spike_count += 1
            # Get MUAP for this motor unit (with no jitter for MVC)
            muap_to_add = MUs[i].calc_muap(0)  # No jitter

            # Add MUAP to EMG signal
            end_idx = min(mvc_emg.shape[0], t + muap_to_add.shape[0])
            muap_end_idx = end_idx - t

            mvc_emg[t:end_idx, :] += muap_to_add[:muap_end_idx, :]

    mu_time = time.time() - mu_start_time
    print(f"MU {i + 1:3d}/{mu_pool.N}: {spike_count} spikes in {mu_time:.2f}s")

emg_generation_time = time.time() - start_time
print(f"\n✓ MVC EMG generated in {emg_generation_time:.1f}s")

## Apply electrode trajectory mixing and differential recording
print("\nApplying electrode signal processing...")
start_time = time.time()

# Apply trajectory mixing and differential recording as in MATLAB
# mvc_emg = mvc_emg * electrode.traj_mixing_mat(0.5, electrode.n_nodes, electrode.n_channels)' * electrode.diff_mat';
traj_mixing_mat = electrode.traj_mixing_mat(
    0.5, electrode.n_nodes, electrode.n_channels
)
mvc_emg = mvc_emg @ traj_mixing_mat.T @ electrode.diff_mat.T

processing_time = time.time() - start_time
print(f"✓ Signal processing completed in {processing_time:.2f}s")
print(f"Final EMG shape: {mvc_emg.shape}")

## Calculate noise parameters
print("\nCalculating noise parameters...")

# Calculate EMG standard deviation (excluding first and last second)
start_idx = int(fs)  # Skip first second
end_idx = int(mvc_emg.shape[0] - fs)  # Skip last second

if end_idx > start_idx:
    mvc_emg_std = np.std(mvc_emg[start_idx:end_idx, :], axis=0)
else:
    mvc_emg_std = np.std(mvc_emg, axis=0)

# Set SNR and calculate noise standard deviation
SNR = 20  # dB
emg_noise_std = mvc_emg_std * 10 ** (-SNR / 20)  # Convert dB to linear scale

# Use average noise std across all channels as in MATLAB
emg_noise_std = np.mean(emg_noise_std) * np.ones_like(emg_noise_std)

print(f"✓ SNR: {SNR} dB")
print(f"✓ Signal std (mean): {np.mean(mvc_emg_std):.6f} μV")
print(f"✓ Noise std: {emg_noise_std[0]:.6f} μV")

## Create visualization
print("\nCreating MVC EMG visualization...")

# Plot first 2 seconds of each channel
plot_duration = 2.0  # seconds
plot_samples = int(plot_duration * fs)
timeline = np.arange(plot_samples) / fs

fig, axes = plt.subplots(
    electrode.n_channels, 1, figsize=(12, 2 * electrode.n_channels)
)
if electrode.n_channels == 1:
    axes = [axes]

fig.suptitle("Maximum Voluntary Contraction (MVC) EMG Signals", fontsize=14)

for ch in range(electrode.n_channels):
    axes[ch].plot(timeline, mvc_emg[:plot_samples, ch], "b-", linewidth=0.5)
    axes[ch].set_ylabel(f"Ch {ch + 1}\n(μV)")
    axes[ch].grid(True, alpha=0.3)

    # Add some statistics to the plot
    ch_rms = np.sqrt(np.mean(mvc_emg[start_idx:end_idx, ch] ** 2))
    axes[ch].set_title(f"RMS: {ch_rms:.2f} μV, Std: {mvc_emg_std[ch]:.2f} μV")

axes[-1].set_xlabel("Time (s)")
plt.tight_layout()
plt.savefig("mvc_emg_signals.png", dpi=150, bbox_inches="tight")
plt.close()
print("Saved MVC EMG visualization to mvc_emg_signals.png")

## Summary statistics
total_spikes = np.sum(mvc_spikes)
spike_rate = total_spikes / (mvc_duration * mu_pool.N)
active_mus = np.sum(np.any(mvc_spikes, axis=0))

print("\n" + "=" * 60)
print("MVC EMG generation completed successfully!")
print(f"✓ Signal duration: {mvc_duration}s ({mvc_T} samples)")
print(f"✓ Sampling frequency: {fs} Hz")
print(f"✓ Number of channels: {electrode.n_channels}")
print(f"✓ Total spikes generated: {total_spikes}")
print(f"✓ Average spike rate: {spike_rate:.1f} spikes/s/MU")
print(f"✓ Active motor units: {active_mus}/{mu_pool.N}")
print(f"✓ SNR: {SNR} dB")
print(f"✓ Generation time: {emg_generation_time:.1f}s")

# Clean up intermediate variables as in MATLAB
# Note: Some variables may not exist if no spikes were generated
try:
    del i, t, muap_to_add
except NameError:
    pass
