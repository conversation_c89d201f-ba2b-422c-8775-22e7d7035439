from myogen.simulators.emg.intramuscular.python.classes import MN_Pool_Sim
from myogen.simulators.emg.intramuscular.python.config import N, rr, rm, Rmuscle
import matplotlib.pyplot as plt
import matplotlib

matplotlib.use("Agg")  # Use non-interactive backend

# Create motor neuron pool object
mn_pool = MN_Pool_Sim(
    N, rr, rm
)  # Creates an object of class MN_Pool, representing the motor neuron pool.

# Distribute innervation centers
mn_pool.distribute_innervation_centers(Rmuscle)

# Show motor unit sizes
fig1, ax1 = plt.subplots()
mn_pool.show_sizes(ax1)
plt.savefig("motor_unit_sizes.png", dpi=150, bbox_inches="tight")
plt.close()
print("Saved motor unit sizes plot to motor_unit_sizes.png")

# Show innervation centers
fig2, ax2 = plt.subplots()
mn_pool.show_centers(ax2, Rmuscle)
plt.savefig("innervation_centers.png", dpi=150, bbox_inches="tight")
plt.close()
print("Saved innervation centers plot to innervation_centers.png")

## Define excitation-firing rate curves for the motor neurons in the pool

# 1) De <PERSON> model
# Use pre-defined DeLuca's model for FDI.
# mn_pool.set_deluca_mdl('fdi')

# 2) Linear force-rate model
# Use more general linear model (Fuglevand 1993 + Keenan and Valero-Cuevas 2007).
# Generate the slopes, as well as the minimum and maximum firing rates.
mn_pool.generate_minfr(
    "linear_rt", -5, 10
)  # Parameter 1: slope, Parameter 2: intersect
mn_pool.generate_maxfr("linear_rt", -10, 40)
mn_pool.generate_frs("linear_rt", -20, 50)

# Plot results
mn_pool.show_fr_exc_curves()
plt.savefig("firing_rate_curves.png", dpi=150, bbox_inches="tight")
plt.close()
print("Saved firing rate curves plot to firing_rate_curves.png")

# Define coefficient of IPI variation (gaussian model is used, following Fuglevand 1993)
mn_pool.CV = 1 / 7

print(f"Motor neuron pool initialized successfully!")
print(f"Number of motor units: {mn_pool.N}")
print(f"Recruitment range: {mn_pool.rr}")
print(f"Recruitment maximum: {mn_pool.rm}")
print(f"Muscle radius: {Rmuscle} mm")
print(f"Coefficient of variation: {mn_pool.CV}")
print(f"Motor unit sizes range: {mn_pool.sz.min():.3f} - {mn_pool.sz.max():.3f}")
print(f"Recruitment thresholds range: {mn_pool.rt.min():.3f} - {mn_pool.rt.max():.3f}")
