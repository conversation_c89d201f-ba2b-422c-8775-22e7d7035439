"""
Script 14: Generate Annotation for Decomposition
Generates full centered and shortened dictionary for decomposition,
reconstructs signals, and provides visualization.
"""

import numpy as np
import matplotlib.pyplot as plt


def find_ap_center_gauss(muap):
    """
    Find the center of action potential using Gaussian fitting approach.
    For single-channel data, finds the peak of the absolute signal.
    """
    if muap.ndim == 1:
        # Single channel case
        return np.argmax(np.abs(muap))
    else:
        # Multi-channel case - find channel with maximum peak-to-peak amplitude
        pp_amplitudes = np.max(muap, axis=0) - np.min(muap, axis=0)
        center_channel = np.argmax(pp_amplitudes)
        return np.argmax(np.abs(muap[:, center_channel]))


def take_scope(signal, center, half_length):
    """
    Extract a scope of signal centered around a specific point.

    Parameters:
    -----------
    signal : array
        Input signal
    center : int
        Center point index
    half_length : int
        Half length of the scope to extract

    Returns:
    --------
    array : Extracted signal scope
    """
    start_idx = max(0, center - half_length)
    end_idx = min(len(signal), center + half_length + 1)

    # Create output array of desired length
    scope_length = 2 * half_length + 1
    scope = np.zeros(scope_length)

    # Calculate indices for placing the extracted segment
    scope_start = max(0, half_length - center)
    scope_end = scope_start + (end_idx - start_idx)

    # Extract and place the signal
    scope[scope_start:scope_end] = signal[start_idx:end_idx]

    return scope


def spikes2firings(spikes):
    """
    Convert spike trains to firing times.
    """
    if spikes.ndim == 2:
        # 2D case: (time, motor_units)
        firings = []
        for mu_idx in range(spikes.shape[1]):
            firing_times = np.where(spikes[:, mu_idx] > 0)[0]
            firings.append(firing_times)
        return firings
    elif spikes.ndim == 3:
        # 3D case: (time, motor_units, channels)
        firings = []
        for ch_idx in range(spikes.shape[2]):
            ch_firings = []
            for mu_idx in range(spikes.shape[1]):
                firing_times = np.where(spikes[:, mu_idx, ch_idx] > 0)[0]
                ch_firings.append(firing_times)
            firings.append(ch_firings)
        return firings


def shift_firings(firings, shifts):
    """
    Shift firing times by specified amounts.

    Parameters:
    -----------
    firings : list
        List of firing times for each motor unit
    shifts : array
        Shift amounts for each motor unit

    Returns:
    --------
    list : Shifted firing times
    """
    shifted_firings = []
    for mu_idx, firing_times in enumerate(firings):
        if len(firing_times) > 0:
            shifted_times = firing_times + shifts[mu_idx]
            # Keep only positive times
            shifted_times = shifted_times[shifted_times >= 0]
            shifted_firings.append(shifted_times)
        else:
            shifted_firings.append(np.array([], dtype=int))
    return shifted_firings


def firings2spikes(firings, signal_length):
    """
    Convert firing times back to spike trains.

    Parameters:
    -----------
    firings : list
        List of firing times for each motor unit
    signal_length : int
        Length of the output spike train

    Returns:
    --------
    array : Spike train matrix (time, motor_units)
    """
    n_mus = len(firings)
    spikes = np.zeros((signal_length, n_mus))

    for mu_idx, firing_times in enumerate(firings):
        valid_times = firing_times[firing_times < signal_length]
        spikes[valid_times.astype(int), mu_idx] = 1

    return spikes


def conv_trains_muaps(spike_trains, dictionary, mode="causal"):
    """
    Convolve spike trains with MUAP dictionary.

    Parameters:
    -----------
    spike_trains : array
        Spike train matrix (time, motor_units)
    dictionary : array
        MUAP dictionary (time, motor_units)
    mode : str
        Convolution mode ('causal' or 'full')

    Returns:
    --------
    array : Reconstructed EMG signal
    """
    n_samples, n_mus = spike_trains.shape
    if dictionary.ndim == 2:
        # Single channel
        n_channels = 1
        dict_length = dictionary.shape[0]
        reconstructed = np.zeros((n_samples, n_channels))

        for mu_idx in range(n_mus):
            if mu_idx < dictionary.shape[1]:
                # Convolve spike train with MUAP
                conv_result = np.convolve(
                    spike_trains[:, mu_idx], dictionary[:, mu_idx], mode="full"
                )

                if mode == "causal":
                    # Take only the causal part (same length as input)
                    reconstructed[:, 0] += conv_result[:n_samples]
                else:
                    # Return full convolution (would need padding handling)
                    reconstructed[:, 0] += conv_result[:n_samples]
    else:
        # Multi-channel
        n_channels = dictionary.shape[1]
        dict_length = dictionary.shape[0]
        reconstructed = np.zeros((n_samples, n_channels))

        for ch in range(n_channels):
            for mu_idx in range(n_mus):
                if mu_idx < dictionary.shape[2]:
                    conv_result = np.convolve(
                        spike_trains[:, mu_idx], dictionary[:, ch, mu_idx], mode="full"
                    )

                    if mode == "causal":
                        reconstructed[:, ch] += conv_result[:n_samples]
                    else:
                        reconstructed[:, ch] += conv_result[:n_samples]

    return reconstructed


print("Generating annotation for decomposition...")

# Generate full centered and shortened dictionary
if electrode.n_channels == 1:
    print("Single channel electrode detected - processing for decomposition...")

    # Calculate dictionary parameters
    dict_muap_half_len = int(np.floor(10 / 2 / 1000 * fs))  # 10ms total, 5ms half
    dict_muap_full_len = 2 * dict_muap_half_len + 1

    print(f"Dictionary MUAP half length: {dict_muap_half_len} samples")
    print(f"Dictionary MUAP full length: {dict_muap_full_len} samples")

    # Initialize dictionaries
    dictionary_for_decomp_full = np.zeros((dict_muap_full_len, mu_pool.N))
    dictionary_for_decomp_detectable = np.zeros(
        (dict_muap_full_len, len(detectable_ind))
    )

    # Process full dictionary
    print("Processing full dictionary...")
    ap_centers_full = np.zeros(mu_pool.N, dtype=int)

    for m in range(mu_pool.N):
        # Get trajectory mixing matrix
        temp_traj = electrode.traj_mixing_mat(
            0, electrode.n_nodes, electrode.n_channels
        )

        # Find AP center for this motor unit
        ap_centers_full[m] = find_ap_center_gauss(dictionary_full_init[:, m])

        # Extract centered scope
        dictionary_for_decomp_full[:, m] = take_scope(
            dictionary_full_init[:, m], ap_centers_full[m], dict_muap_half_len
        )

    # Process detectable dictionary
    print("Processing detectable dictionary...")
    ap_centers_detectable = np.zeros(len(detectable_ind), dtype=int)

    for m in range(len(detectable_ind)):
        # Get trajectory mixing matrix
        temp_traj = electrode.traj_mixing_mat(
            0, electrode.n_nodes, electrode.n_channels
        )

        # Find AP center for this detectable motor unit
        ap_centers_detectable[m] = find_ap_center_gauss(
            dictionary_detectable_init[:, m]
        )

        # Extract centered scope
        dictionary_for_decomp_detectable[:, m] = take_scope(
            dictionary_detectable_init[:, m],
            ap_centers_detectable[m],
            dict_muap_half_len,
        )

    print(
        f"Full dictionary for decomposition shape: {dictionary_for_decomp_full.shape}"
    )
    print(
        f"Detectable dictionary for decomposition shape: {dictionary_for_decomp_detectable.shape}"
    )

    # Generate annotation
    print("Generating firing annotations...")

    # Convert spikes to firings
    firings_full = spikes2firings(spikes)
    firings_detectable = spikes2firings(spikes[:, detectable_ind])

    # Calculate shifts
    shifts_full = -dict_muap_half_len + ap_centers_full
    shifts_detectable = -dict_muap_half_len + ap_centers_detectable

    # Shift firings
    firings_for_decomp_full = shift_firings(firings_full, shifts_full)
    firings_for_decomp_detectable = shift_firings(firings_detectable, shifts_detectable)

    # Convert back to spikes
    spikes_for_decomp_full = firings2spikes(firings_for_decomp_full, len(spikes))
    spikes_for_decomp_detectable = firings2spikes(
        firings_for_decomp_detectable, len(spikes)
    )

    print("✓ Annotation generation completed")

    # Reconstruct EMG signals
    print("Reconstructing EMG signals...")

    reconstructed_for_decomp_full = conv_trains_muaps(
        spikes_for_decomp_full, dictionary_for_decomp_full, "causal"
    )

    reconstructed_for_decomp_detectable = conv_trains_muaps(
        spikes_for_decomp_detectable, dictionary_for_decomp_detectable, "causal"
    )

    print("✓ EMG reconstruction completed")

    # Visualization
    print("Creating visualization...")

    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(13, 6))

    # Calculate separator for multichannel view
    separator_step = np.mean(np.std(reconstructed_for_decomp_full, axis=0)) * 5
    separator = np.arange(1, electrode.n_channels + 1) * separator_step
    separator = np.tile(separator, (reconstructed_for_decomp_full.shape[0], 1))

    # Plot EMG with detectable reconstruction (top subplot)
    ax1.plot(
        profile.timeline, emg + separator, "k", linewidth=1, label="Original signal"
    )
    ax1.plot(
        profile.timeline,
        reconstructed_for_decomp_detectable + separator,
        linewidth=1,
        color="b",
        label="Reconstr. from detectable dict.",
    )
    ax1.plot(
        profile.timeline,
        np.sum(spikes_for_decomp_detectable, axis=1, keepdims=True) * np.std(emg)
        + separator,
        linewidth=1.2,
        label="Spikes",
    )
    ax1.legend()
    ax1.set_title("EMG Reconstruction - Detectable Dictionary")
    ax1.set_ylabel("Amplitude, Arbitrary units")

    # Plot EMG with full reconstruction (bottom subplot)
    ax2.plot(
        profile.timeline, emg + separator, "k", linewidth=1, label="Original signal"
    )
    ax2.plot(
        profile.timeline,
        reconstructed_for_decomp_full + separator,
        linewidth=1,
        color="g",
        label="Reconstr. from full dict.",
    )
    ax2.plot(
        profile.timeline,
        np.sum(spikes_for_decomp_full, axis=1, keepdims=True) * np.std(emg) + separator,
        linewidth=1.2,
        label="Spikes",
    )
    ax2.legend()
    ax2.set_title("EMG Reconstruction - Full Dictionary")
    ax2.set_xlabel("Time, s")
    ax2.set_ylabel("Amplitude, Arbitrary units")

    plt.suptitle("Reconstructed EMG vs Simulated")
    plt.tight_layout()
    plt.savefig("emg_reconstruction_decomposition.png", dpi=300, bbox_inches="tight")
    plt.show()

    print(
        "✓ Visualization completed and saved as 'emg_reconstruction_decomposition.png'"
    )

    # Clean up temporary variables
    del temp_traj

    print(f"✓ Decomposition annotation generation completed!")
    print(f"  - Full dictionary AP centers: {len(ap_centers_full)}")
    print(f"  - Detectable dictionary AP centers: {len(ap_centers_detectable)}")
    print(
        f"  - Dictionary length: {dict_muap_full_len} samples ({dict_muap_full_len / fs * 1000:.1f} ms)"
    )

else:
    print("Multi-channel electrode detected - decomposition annotation skipped")
    print("(Original MATLAB script only processes single-channel electrodes)")
