"""
PID Controller Tuning for Excitation-Force Model.
This script translates the MATLAB version s04_cl_tune_pid.m to Python.

Generates identification and validation data for the excitation-force model,
estimates the plant model, and tunes a PID controller.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from scipy import signal
from scipy.optimize import minimize
import warnings

# Use non-interactive backend for server environments
matplotlib.use("Agg")

from myogen.simulators.emg.intramuscular.python.config import fs, fsl


def safe_lsim(tf_system, input_data, time_vector):
    """
    Safely handle signal.lsim which can return 2 or 3 values.

    Returns:
    --------
    tuple : (time_vector, output_data)
        Always returns exactly 2 values regardless of system representation
    """
    result = signal.lsim(tf_system, input_data, time_vector)
    if len(result) == 2:
        return result
    else:  # len(result) == 3
        return result[0], result[1]  # Return time and output, ignore state


def check_dependencies():
    """Check if required objects exist from previous scripts."""
    try:
        mu_pool
    except NameError:
        raise NameError(
            "mu_pool not found. Please run s02_cl_init_mu_pool.py first to create the motor unit pool."
        )

    try:
        mf_mdl
    except NameError:
        raise NameError(
            "mf_mdl not found. Please run s03_cl_init_force_model.py first to create the muscle force model."
        )

    return True


def multilevel_prbs(length, max_freq, amplitude=1, levels=2):
    """
    Generate a pseudo-random binary sequence (PRBS) similar to MATLAB's multilevel_prbs.

    Parameters:
    -----------
    length : int
        Length of the sequence
    max_freq : float
        Maximum frequency content
    amplitude : float
        Amplitude of the sequence
    levels : int
        Number of levels (default 2 for binary)

    Returns:
    --------
    numpy.ndarray
        Pseudo-random binary sequence
    """
    # Generate a random sequence and filter it
    np.random.seed(42)  # For reproducibility
    random_signal = np.random.randn(length)

    # Design a low-pass filter to limit frequency content
    nyquist = fs / 2
    cutoff = min(max_freq, nyquist * 0.8)
    sos = signal.butter(4, cutoff / nyquist, btype="low", output="sos")
    filtered_signal = signal.sosfilt(sos, random_signal)

    # Quantize to binary levels
    threshold = np.median(filtered_signal)
    prbs = np.where(filtered_signal > threshold, amplitude, 0)

    return prbs


def resample_data(data, original_fs, target_fs):
    """
    Resample data from original_fs to target_fs.

    Parameters:
    -----------
    data : numpy.ndarray
        Input data to resample
    original_fs : float
        Original sampling frequency
    target_fs : float
        Target sampling frequency

    Returns:
    --------
    numpy.ndarray
        Resampled data
    """
    if original_fs == target_fs:
        return data

    # Calculate resampling ratio
    resample_ratio = target_fs / original_fs
    new_length = int(len(data) * resample_ratio)

    # Use scipy's resample function
    resampled_data = signal.resample(data, new_length)

    return resampled_data


def estimate_oe_model(input_data, output_data, nb=1, nf=1, nk=0):
    """
    Estimate an Output-Error (OE) model using optimization.

    Parameters:
    -----------
    input_data : numpy.ndarray
        Input signal
    output_data : numpy.ndarray
        Output signal
    nb : int
        Number of B polynomial coefficients
    nf : int
        Number of F polynomial coefficients
    nk : int
        Input delay

    Returns:
    --------
    dict
        Dictionary containing model parameters
    """

    def oe_cost_function(params):
        """Cost function for OE model estimation."""
        b_coeffs = params[:nb]
        f_coeffs = np.concatenate([[1], params[nb : nb + nf]])

        # Create transfer function
        try:
            tf_system = signal.TransferFunction(b_coeffs, f_coeffs)

            # Simulate the system
            time_vector = np.arange(len(input_data)) / fsl
            _, y_pred = safe_lsim(tf_system, input_data, time_vector)

            # Calculate cost (mean squared error)
            cost = np.mean((output_data - y_pred) ** 2)

        except:
            cost = 1e6  # Large penalty for invalid parameters

        return cost

    # Initial parameter guess
    initial_params = np.concatenate(
        [
            np.random.randn(nb) * 0.1,  # B coefficients
            np.random.randn(nf) * 0.1,  # F coefficients (excluding f0=1)
        ]
    )

    # Optimize parameters
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        result = minimize(oe_cost_function, initial_params, method="L-BFGS-B")

    # Extract optimized parameters
    b_coeffs = result.x[:nb]
    f_coeffs = np.concatenate([[1], result.x[nb : nb + nf]])

    # Create final transfer function
    tf_system = signal.TransferFunction(b_coeffs, f_coeffs)

    return {
        "tf": tf_system,
        "b": b_coeffs,
        "f": f_coeffs,
        "cost": result.fun,
        "success": result.success,
    }


def predict_model(model_tf, input_data, prediction_horizon=0):
    """
    Predict model output given input data.

    Parameters:
    -----------
    model_tf : scipy.signal.TransferFunction
        Transfer function model
    input_data : numpy.ndarray
        Input signal
    prediction_horizon : int
        Prediction horizon (0 for simulation)

    Returns:
    --------
    numpy.ndarray
        Predicted output
    """
    time_vector = np.arange(len(input_data)) / fsl
    _, y_pred = safe_lsim(model_tf, input_data, time_vector)

    return y_pred


def tune_pid_controller(plant_tf, controller_type="PI", bandwidth=8):
    """
    Tune a PID controller for the given plant.

    Parameters:
    -----------
    plant_tf : scipy.signal.TransferFunction
        Plant transfer function
    controller_type : str
        Type of controller ('P', 'PI', 'PID')
    bandwidth : float
        Desired bandwidth

    Returns:
    --------
    dict
        Dictionary containing PID parameters
    """
    # Simple PID tuning based on pole placement
    # This is a simplified version of MATLAB's pidtune

    # Get plant characteristics
    poles = plant_tf.poles
    zeros = plant_tf.zeros

    # Calculate DC gain manually: G(0) = num(0) / den(0)
    try:
        dc_gain = (
            plant_tf.num[-1] / plant_tf.den[-1]
        )  # Last coefficients are constant terms
    except (ZeroDivisionError, IndexError):
        dc_gain = 1.0  # Default fallback

    # Simple tuning rules (Ziegler-Nichols inspired)
    if controller_type.upper() == "PI":
        # For PI controller
        Kp = bandwidth / abs(dc_gain)
        Ki = Kp * bandwidth / 4
        Kd = 0
    elif controller_type.upper() == "PID":
        # For PID controller
        Kp = bandwidth / abs(dc_gain)
        Ki = Kp * bandwidth / 4
        Kd = Kp / (4 * bandwidth)
    else:  # Proportional only
        Kp = bandwidth / abs(dc_gain)
        Ki = 0
        Kd = 0

    return {"Kp": Kp, "Ki": Ki, "Kd": Kd}


def create_pid_tf(pid_params):
    """
    Create a PID controller transfer function.

    Parameters:
    -----------
    pid_params : dict
        Dictionary with Kp, Ki, Kd values

    Returns:
    --------
    scipy.signal.TransferFunction
        PID controller transfer function
    """
    Kp, Ki, Kd = pid_params["Kp"], pid_params["Ki"], pid_params["Kd"]

    # PID transfer function: Kp + Ki/s + Kd*s
    # In discrete time or using approximation
    if Ki == 0 and Kd == 0:
        # Proportional only
        return signal.TransferFunction([Kp], [1])
    elif Kd == 0:
        # PI controller: Kp + Ki/s = (Kp*s + Ki)/s
        return signal.TransferFunction([Kp, Ki], [1, 0])
    else:
        # Full PID: Kp + Ki/s + Kd*s = (Kd*s² + Kp*s + Ki)/s
        return signal.TransferFunction([Kd, Kp, Ki], [1, 0])


def feedback_system(controller_tf, plant_tf):
    """
    Create a closed-loop feedback system.

    Parameters:
    -----------
    controller_tf : scipy.signal.TransferFunction
        Controller transfer function
    plant_tf : scipy.signal.TransferFunction
        Plant transfer function

    Returns:
    --------
    scipy.signal.TransferFunction
        Closed-loop transfer function
    """
    # Forward path: controller * plant
    forward_tf = signal.TransferFunction(
        np.convolve(controller_tf.num, plant_tf.num),
        np.convolve(controller_tf.den, plant_tf.den),
    )

    # Closed-loop: T = G/(1+G) where G is the forward path
    # T(s) = N(s) / (D(s) + N(s))
    forward_num = forward_tf.num
    forward_den = forward_tf.den

    # Ensure numerator and denominator have the same length by padding with zeros
    max_len = max(len(forward_num), len(forward_den))

    # Pad the shorter array with leading zeros
    if len(forward_num) < max_len:
        forward_num = np.concatenate(
            [np.zeros(max_len - len(forward_num)), forward_num]
        )
    if len(forward_den) < max_len:
        forward_den = np.concatenate(
            [np.zeros(max_len - len(forward_den)), forward_den]
        )

    # Closed-loop transfer function: T(s) = N(s) / (D(s) + N(s))
    closed_loop_num = forward_num
    closed_loop_den = forward_den + forward_num

    return signal.TransferFunction(closed_loop_num, closed_loop_den)


# Only run dependency checks and main execution when script is run directly
if __name__ == "__main__" or "mu_pool" in globals():
    # Check dependencies when running as main script
    if __name__ != "__main__":
        check_dependencies()

    print("Starting PID controller tuning for excitation-force model...")
    print("=" * 60)

    # Generate the identification and validation data for the excitation-force model
    print("Generating system identification data...")
    sys_T = int(32 * fs)

sys_pseudorandom_input = multilevel_prbs(sys_T, fs / 2, 1)

sys_l_input = round(len(sys_pseudorandom_input) / 2)
sys_ident_excitation = sys_pseudorandom_input[:sys_l_input]
sys_valid_excitation = sys_pseudorandom_input[sys_l_input:]

# Set initial samples to zero
sys_ident_excitation[: round(fs / 2)] = 0
sys_valid_excitation[: round(fs / 2)] = 0
sys_timeline = np.linspace(0, len(sys_ident_excitation) / fs, len(sys_ident_excitation))

print(f"Generated {len(sys_ident_excitation)} samples for identification")
print(f"Generated {len(sys_valid_excitation)} samples for validation")

# Generate identification and validation spike trains
print("Generating spike trains...")
sys_ident_spikes, _, _ = mu_pool.mn_pool.generate_spike_train_gauss(
    np.arange(1, sys_T // 2 + 1), np.full(mu_pool.N, np.nan), sys_ident_excitation, fs
)

sys_valid_spikes, _, _ = mu_pool.mn_pool.generate_spike_train_gauss(
    np.arange(1, sys_T // 2 + 1), np.full(mu_pool.N, np.nan), sys_valid_excitation, fs
)

# Generate identification and validation force
print("Generating force signals...")
sys_ident_force = mf_mdl.generate_force_offline(sys_ident_spikes)
sys_valid_force = mf_mdl.generate_force_offline(sys_valid_spikes)

print(
    f"Force range - Identification: [{np.min(sys_ident_force):.3f}, {np.max(sys_ident_force):.3f}]"
)
print(
    f"Force range - Validation: [{np.min(sys_valid_force):.3f}, {np.max(sys_valid_force):.3f}]"
)

# Downsample the data for controller design
print("Downsampling data for controller design...")
downsample_factor = int(fs / fsl)

sys_ident_excitation_r = resample_data(sys_ident_excitation, fs, fsl)
sys_ident_force_r = resample_data(sys_ident_force, fs, fsl)
sys_valid_excitation_r = resample_data(sys_valid_excitation, fs, fsl)
sys_valid_force_r = resample_data(sys_valid_force, fs, fsl)

print(f"Downsampled from {fs} Hz to {fsl} Hz")
print(f"Identification data length: {len(sys_ident_force_r)} samples")

# Estimate the plant (excitation-force) model
print("Estimating excitation-to-force plant model...")
e2f_model = estimate_oe_model(
    sys_ident_excitation_r, sys_ident_force_r, nb=1, nf=1, nk=0
)

if e2f_model["success"]:
    print(f"✓ Model estimation successful (cost: {e2f_model['cost']:.6f})")
    print(f"  B coefficients: {e2f_model['b']}")
    print(f"  F coefficients: {e2f_model['f']}")
else:
    print("⚠ Model estimation had convergence issues")

# Compare the estimated model's output with the validation data
print("Validating model on validation dataset...")
pid_pred = predict_model(e2f_model["tf"], sys_valid_excitation_r)

# Calculate validation metrics
mse = np.mean((sys_valid_force_r - pid_pred) ** 2)
r_squared = 1 - (
    np.sum((sys_valid_force_r - pid_pred) ** 2)
    / np.sum((sys_valid_force_r - np.mean(sys_valid_force_r)) ** 2)
)

print(f"Validation MSE: {mse:.6f}")
print(f"Validation R²: {r_squared:.3f}")

# Set up and tune the PID controller
print("Tuning PI controller...")
pid_params = tune_pid_controller(e2f_model["tf"], "PI", 8)
print(f"PI controller parameters:")
print(f"  Kp: {pid_params['Kp']:.4f}")
print(f"  Ki: {pid_params['Ki']:.4f}")
print(f"  Kd: {pid_params['Kd']:.4f}")

# Create PID controller transfer function
pidc = create_pid_tf(pid_params)

# Test the PID on the linear model
print("Creating closed-loop system...")
try:
    pid_test = feedback_system(pidc, e2f_model["tf"])
    print("✓ Closed-loop system created successfully")

    # Check system stability
    poles = pid_test.poles
    stable = np.all(np.real(poles) < 0)
    print(f"System stability: {'✓ Stable' if stable else '⚠ Unstable'}")

except Exception as e:
    print(f"⚠ Error creating closed-loop system: {e}")
    pid_test = None

# Optional: Save results for later use
print("Saving results...")
np.savez(
    "pid_tuning_results.npz",
    sys_ident_excitation=sys_ident_excitation_r,
    sys_ident_force=sys_ident_force_r,
    sys_valid_excitation=sys_valid_excitation_r,
    sys_valid_force=sys_valid_force_r,
    model_prediction=pid_pred,
    pid_params=pid_params,
    model_b=e2f_model["b"],
    model_f=e2f_model["f"],
)

print("✓ Results saved to pid_tuning_results.npz")

# Clean up variables (as in MATLAB)
del sys_l_input

print("\n" + "=" * 60)
print("PID controller tuning completed successfully!")
print(
    f"Plant model estimated with {e2f_model['b'].shape[0]} B and {e2f_model['f'].shape[0]} F coefficients"
)
print(f"PI controller tuned with Kp={pid_params['Kp']:.4f}, Ki={pid_params['Ki']:.4f}")
if pid_test is not None:
    print("Closed-loop system is ready for testing")
else:
    print("Note: Closed-loop system creation encountered issues")
