# EMG Intramuscular Simulation - Python Implementation

This directory contains a comprehensive Python implementation of the EMG intramuscular simulation framework, providing a complete pipeline for simulating motor unit pools, muscle force generation, electrode recordings, and EMG signal reconstruction.

## Overview

The EMG intramuscular simulation provides a physiologically-based model for generating synthetic EMG signals from intramuscular electrodes. The simulation includes motor neuron pools, muscle fiber distributions, force generation models, electrode configurations, and signal processing capabilities.

## Directory Structure

```
emgenius/simulators/emg/intramuscular/python/
├── main.py                    # Main pipeline execution script
├── config.py                  # Global configuration parameters
├── classes/                   # Core simulation classes
│   ├── MN_Pool_Sim.py        # Motor neuron pool simulation
│   ├── MU_Pool_Sim.py        # Motor unit pool simulation  
│   ├── MuscleForceMdl.py     # Muscle force model
│   ├── IntramuscularArray.py # Electrode array simulation
│   ├── MU_Sim.py             # Individual motor unit simulation
│   ├── ContractionProfile.py # Contraction profile generation
│   └── muscle_functions.py   # Helper functions
├── sections/                  # Pipeline scripts (s01-s15)
│   ├── s01_cl_init_mn_pool.py           # Initialize motor neuron pool
│   ├── s02_cl_init_mu_pool.py           # Initialize motor unit pool
│   ├── s03_cl_init_force_model.py       # Initialize force model
│   ├── s04_cl_tune_pid.py               # PID controller tuning
│   ├── s05_cl_init_profile.py           # Initialize contraction profile
│   ├── s06_cl_generate_force.py         # Generate force signals
│   ├── s07_cl_init_electrode.py         # Initialize electrode array
│   ├── s08_cl_init_muaps.py             # Initialize MUAPs
│   ├── s09_cl_generate_mvc_emg.py       # Generate MVC EMG
│   ├── s10_cl_generate_emg.py           # Generate EMG signals
│   ├── s11_cl_get_detectable_mus.py     # Identify detectable MUs
│   ├── s12_cl_generate_annotation.py    # Generate annotations
│   ├── s13_cl_generate_dictionary.py    # Generate MUAP dictionary
│   ├── s14_cl_generate_annotation_for_decomp.py # Decomposition annotations
│   └── s15_cl_reconstruct_signal.py     # Signal reconstruction
└── README.md                  # This documentation
```

## Key Features

### Core Simulation Components
- **Motor Neuron Pool**: Physiologically-based motor neuron recruitment and firing patterns
- **Motor Unit Pool**: Muscle fiber distributions, innervation areas, and fiber-to-neuron assignments
- **Force Generation**: Twitch-based force models with PID control for excitation-force relationships
- **Electrode Arrays**: Configurable intramuscular electrode geometries with trajectory simulation
- **MUAP Generation**: Motor unit action potential simulation with realistic propagation
- **EMG Synthesis**: Complete EMG signal generation from spike trains and MUAPs

### Advanced Features
- **Signal Decomposition**: Tools for EMG signal decomposition and reconstruction
- **Jitter Simulation**: Realistic temporal variability in motor unit firing
- **Electrode Movement**: Trajectory-based electrode positioning for enhanced signal quality
- **Force Control**: PID-tuned excitation profiles for precise force generation
- **Visualization**: Comprehensive plotting capabilities for all simulation components

## Dependencies

```python
numpy>=1.20.0
pandas>=1.3.0
matplotlib>=3.5.0
scipy>=1.7.0
scikit-learn>=1.0.0
scikit-fmm>=2022.3.26
```

## Quick Start

### Running the Complete Pipeline

```bash
cd myogen/simulators/emg/intramuscular/python
python main.py
```

This executes all 15 pipeline scripts in sequence, generating a complete EMG simulation with visualizations.

### Minimal Working Example

> [!IMPORTANT]  
> See `test_muap_values.py` for a minimal working example.

### Configuration

Edit `config.py` to customize simulation parameters:

```python
# Sampling parameters
fs = 10000          # EMG sampling frequency [Hz]
fsl = 50           # Force sampling frequency [Hz]

# Muscle geometry
Dmf = 400          # Fiber density [fibers/mm²]
Nmf = 34000        # Total muscle fibers
Lmuscle = 30       # Muscle length [mm]
Rmuscle = 5.2      # Muscle radius [mm]

# Motor neuron pool
N = 100            # Number of motor units
rr = 50            # Recruitment range
rm = 0.75          # Recruitment maximum
```

## Core Classes

### MN_Pool_Sim
Motor neuron pool simulation with recruitment and firing rate models.

```python
from classes import MN_Pool_Sim

# Create motor neuron pool
mn_pool = MN_Pool_Sim(N=100, rr=50, rm=0.75)
mn_pool.distribute_innervation_centers(Rmuscle=5.2)
mn_pool.generate_minfr("linear_rt", -5, 10)
mn_pool.generate_maxfr("linear_rt", -10, 40)
```

### MU_Pool_Sim  
Motor unit pool with muscle fiber generation and innervation.

```python
from classes import MU_Pool_Sim

# Initialize with motor neuron pool
mu_pool = MU_Pool_Sim(mn_pool)
mu_pool.generate_mfs(Rmuscle=5.2, Dmf=400)
mu_pool.calc_innervation_areas()
mu_pool.assign_mfs2mns()
```

### MuscleForceMdl
Muscle force generation with twitch-based models.

```python
from classes import MuscleForceMdl

# Create force model
force_model = MuscleForceMdl(N=100, rr=50, fs=10000)
force = force_model.generate_force_offline(spike_trains)
```

### IntramuscularArray
Configurable electrode arrays with trajectory simulation.

```python
from classes import IntramuscularArray

# Create electrode array
electrode = IntramuscularArray(n_electrodes=4, spacing=0.5)
electrode.set_linear_trajectory(distance=2.0, steps=10)
```

## Pipeline Scripts

The simulation pipeline consists of 15 sequential scripts:

1. **s01**: Initialize motor neuron pool with recruitment thresholds and firing rates
2. **s02**: Generate muscle fibers and assign to motor neurons  
3. **s03**: Initialize force model with twitch responses and MVC normalization
4. **s04**: Tune PID controller for excitation-force relationship
5. **s05**: Create contraction profile for desired force trajectory
6. **s06**: Generate force signals using the tuned model
7. **s07**: Initialize intramuscular electrode array configuration
8. **s08**: Generate motor unit action potentials (MUAPs)
9. **s09**: Generate maximum voluntary contraction (MVC) EMG
10. **s10**: Generate EMG signals for the contraction profile
11. **s11**: Identify detectable motor units based on signal quality
12. **s12**: Generate spike train annotations
13. **s13**: Create MUAP dictionary for decomposition
14. **s14**: Generate annotations for decomposition algorithms
15. **s15**: Reconstruct EMG signals from spike trains and MUAPs

## Visualization Outputs

The pipeline generates comprehensive visualizations:

- `motor_unit_sizes.png` - Motor unit size distribution
- `innervation_centers.png` - Spatial distribution of innervation centers
- `firing_rate_curves.png` - Excitation-firing rate relationships
- `muscle_fiber_centers.png` - Muscle fiber spatial distribution
- `innervation_areas_2d.png` - 2D innervation territory visualization
- `muscle_twitches.png` - Individual motor unit twitch responses
- `contraction_profile.png` - Target force trajectory
- `force_generation_results.png` - Generated force vs. target
- `electrode_configuration.png` - Electrode array geometry
- `muaps_visualization.png` - Motor unit action potentials
- `mvc_emg_signals.png` - Maximum voluntary contraction EMG
- `emg_signals_with_profile.png` - EMG signals with force profile
- `spike_raster_plot.png` - Motor unit firing patterns
- `emg_reconstruction_decomposition.png` - Signal reconstruction results
- `jitter_demonstration.png` - Temporal jitter effects

## Performance Optimization

### Fiber Assignment Optimization
The fiber assignment process offers multiple integration methods:

```python
# Fast mode (fastest, reduced accuracy)
mu_pool.assign_mfs2mns(fast_mode=True)

# QMC integration (optimal for 2D, recommended)
mu_pool.assign_mfs2mns(use_qmc_quad=True)

# Vectorized integration (modern scipy approach)
mu_pool.assign_mfs2mns(use_quad_vec=True)

# Monte Carlo integration (default, balanced)
mu_pool.assign_mfs2mns()
```

### Memory Considerations
- Reduce `Dmf` (fiber density) for faster development/testing
- Use `fast_mode=True` for initial parameter exploration
- Consider reducing `N` (motor units) for large-scale parameter studies

## Advanced Usage

### Custom Electrode Configurations

```python
# Linear array
electrode = IntramuscularArray(n_electrodes=8, spacing=0.5, arrangement='linear')

# Grid array  
electrode = IntramuscularArray(n_electrodes=16, spacing=0.3, arrangement='grid')

# Set custom position and orientation
electrode.set_position([0, 0, 5], [0, 0, np.pi/4])
```

### Force Profile Customization

```python
from classes import ContractionProfile

# Create custom contraction profile
profile = ContractionProfile(duration=10.0, fs=50)
profile.add_ramp(start_time=1.0, end_time=3.0, start_force=0.0, end_force=0.5)
profile.add_plateau(start_time=3.0, end_time=7.0, force_level=0.5)
```

### PID Controller Tuning

```python
# Custom PID tuning with system identification
from sections.s04_cl_tune_pid_standalone import tune_pid_controller

# Generate test signals and tune controller
controller_params = tune_pid_controller(force_model, target_bandwidth=2.0)
```

## Signal Processing Features

### EMG Decomposition Support
- MUAP template generation for decomposition algorithms
- Spike train annotations with precise timing
- Signal reconstruction for validation
- Detectable motor unit identification

### Jitter and Variability
- Gaussian inter-pulse interval variability
- Realistic motor unit recruitment patterns
- Physiological firing rate modulation

## Validation and Testing

### Model Validation
- MVC normalization ensures physiological force levels
- Quasistatic excitation-force relationships
- MUAP amplitude and shape validation
- Recruitment threshold verification

### Performance Metrics
- Signal-to-noise ratio calculations
- Motor unit detectability assessment
- Reconstruction accuracy measures
- Decomposition performance evaluation

## References

TODO

## Troubleshooting

### Common Issues

1. **Memory errors**: Reduce `Dmf` or `N` parameters
2. **Slow fiber assignment**: Use `fast_mode=True` or `use_qmc_quad=True`
3. **Import errors**: Ensure all dependencies are installed
4. **Visualization issues**: Check matplotlib backend configuration

### Performance Tips

- Use `matplotlib.use('Agg')` for headless environments
- Enable fast mode for parameter exploration
- Consider parallel processing for large parameter studies
- Monitor memory usage with large fiber populations
