import numpy as np


fs: float = 10000  # Time sampling frequency for EMG, [Hz]
dt: float = 1 / fs  # Sampling period of EMG [s]
fsl: float = 50  # Time sampling frequency for force, [Hz]
dz: float = 0.5  # Spatial sampling frequency for muscle fibers action potentials [mm]

# Muscle geometry
# ------------------------------------------------------------
# Everything is in millimeters.
# Muscle in vicinity of the electrode is approximated as a cylinder with
Dmf: int = 400  # Density of muscle fibers per square millimetre (<PERSON><PERSON> 2005)
Nmf: int = 34000  # Expected number of muscle fibers in the muscle (40k for FDI, see Feinstein - Morphologic studies ... 1995)
Lmuscle: float = 30  # [mm]
Rmuscle: float = np.sqrt((Nmf / Dmf) / np.pi)  # [mm] in Matlab was hardcoded as 5

# MN pool parameters
# ------------------------------------------------------------
N: int = (
    100  # Number of MUs (120 for FDI, see <PERSON><PERSON>tein - Morphologic studies ... 1995)
)
rr: float = 50  # Magnitude of RT distribution: largest/smallest
rm: float = 0.75  # Recruitment maximum (when all the MUs are active)
