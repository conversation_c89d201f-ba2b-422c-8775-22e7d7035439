import numpy as np
import matplotlib.pyplot as plt
from typing import Literal, Optional, Tuple, Union
import skfmm

from myogen.utils import RANDOM_GENERATOR


class MN_Pool_Sim:
    """
    A class MotorNeuronPool_Sim is a container for the Motor Neuron Pool model
    """

    def __init__(self, N: int = 100, rr: float = 50, rm: float = 0.75):
        """
        Initialize Motor Neuron Pool Simulation

        Parameters
        ----------
        N : int
            Number of motor units. By default, 100.
            For FDI set to 120 (Feinstein - Morphologic studies ... 1995)
        rr : float
            Recruitment range: largest/smallest. By default, 50.
        rm : float
            Recruitment maximum (when all the MUs are active). By default, 0.75.
        """
        # Properties
        self.N = N  # Number of motor units
        self.rr = rr  # Recruitment range: largest/smallest
        self.rm = rm  # Recruitment maximum (when all the MUs are active)
        self.centers = None

        self.sz = None  # Sizes
        self.rt = None  # Thresholds

        # Excitation-rate curve parameters
        self.type: Literal["linear_rt", "deluca"] | None = (
            None  # Either linear rectified or deluca
        )
        self.excfr_pars = None  # For DeLuca model
        self.minfr = None  # Minimal firing rates
        self.maxfr = None  # Maximal firing rates
        self.frs = None  # Firing rates slope

        # IPI generation parameters
        self.CV = (
            1 / 6
        )  # Coefficient of variation for gaussian model of interspike interval

        self.exc_fr_curves = None  # Excitation - rate curves of motor neurons

        # Initialize the pool
        self.init_pool()

    def init_pool(self):
        """Initialize the motor unit pool with recruitment thresholds and sizes"""
        rt, rtz = self.generate_mu_recruitment_thresholds(
            N=self.N, RR=self.rr, RM=self.rm
        )
        # In the context of MN_Pool_Sim, sizes are represented by rt and thresholds by rtz
        self.sz = rt.flatten()  # Motor unit sizes
        self.rt = rtz.flatten()  # Recruitment thresholds (starting from zero)

    @staticmethod
    def generate_mu_recruitment_thresholds(
        N: int,
        RR: float,
        RM: float,
        mode: Literal["fuglevand", "ls2n"] = "ls2n",
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Generate motor unit recruitment thresholds and sizes

        Generates N motor neurons' threshold values. This model is a slight variation
        of the one proposed in Fuglevand 1993 (Models of Recruitment and Rate Coding ...).
        Their formula is: rt(i) = exp(i * ln(RR) / N).

        My formula adjusts both the recruitment range (RR = rt(N)/rt(1)) and the
        value of maximum RM = rt(N).

        Parameters
        ----------
        N : int
            Number of motor neurons
        RR : float
            Range of recruitment thresholds (rt(N)/rt(1)). This is the ratio of the
            largest to smallest motor unit in the pool. 
            So the largest motor unit is RR times the smallest motor unit.
        RM : float
            Threshold of the largest motor neuron. This is the maximum recruitment threshold.
        mode : Literal["fuglevand", "ls2n"], optional
            Mode for threshold generation (default: "ls2n").

        Returns
        -------
        Tuple[np.ndarray, np.ndarray]
            rt : np.ndarray
                Recruitment thresholds
            rtz : np.ndarray
                Almost the same, but starts from zero, convenient in simulation
        """
        # MATLAB: i = 1:N;
        i = np.arange(1, N + 1)

        match mode:
            case "fuglevand":
                rt = np.exp(i * np.log(RR) / N)
                rtz = rt - rt[0]
            case "ls2n":
                rt = (RM / RR) * np.exp((i - 1) * np.log(RR) / (N - 1))
                # Version starting from zero
                rtz = (RM / RR) * (np.exp((i - 1) * np.log(RR + 1) / N) - 1)

            case _:
                raise ValueError(f"Unknown mode: {mode}")

        # Normalize the thresholds to the maximum threshold
        rtz = rtz * np.max(rt) / np.max(rtz)

        return rt, rtz

    def distribute_innervation_centers(self, Rmuscle: float):
        """
        Distribute innervation centers using fast marching method

        Parameters
        ----------
        Rmuscle : float
            Muscle radius.
        """
        n = 256
        density_map = np.ones((n, n))
        X, Y = np.meshgrid(np.arange(1, n + 1), np.arange(1, n + 1))
        density_map[np.sqrt((X - n / 2) ** 2 + (Y - n / 2) ** 2) > n / 2 - 1] = 0

        vertices = np.zeros((2, self.N + 1))  # Need N+1 vertices to match MATLAB
        vertices[:, 0] = [1, 1]

        # MATLAB: for i = 2:(obj.N+1)
        for i in range(1, self.N + 1):  # This gives us indices 1 to N (inclusive)
            # Use scikit-fmm for fast marching
            # Create speed map, avoiding division by zero
            speed_map = np.ones_like(density_map)
            valid_mask = density_map > 0
            speed_map[valid_mask] = 1.0 / density_map[valid_mask]
            speed_map[~valid_mask] = 1e-10  # Small value for invalid regions

            D = self.perform_fast_marching(speed_map, vertices[:, :i])
            ind = np.argmax(D)
            x, y = np.unravel_index(ind, (n, n))
            vertices[:, i] = [x, y]

        # MATLAB: obj.centers = vertices(:,end:-1:2)';
        # This takes columns from end down to 2 (1-indexed), then transposes
        # In Python: vertices[:, -1:0:-1] gives us columns from end down to 1 (0-indexed)
        self.centers = vertices[
            :, -1:0:-1
        ].T  # Reverse order, skip first vertex, transpose

        # Only proceed if we have valid centers
        if self.centers.shape[0] > 0 and self.centers.shape[1] == 2:
            center_offset = self.centers - n / 2
            max_dist = np.max(
                np.sqrt(center_offset[:, 0] ** 2 + center_offset[:, 1] ** 2)
            )
            if max_dist > 0:  # Avoid division by zero
                self.centers = center_offset / max_dist * Rmuscle
            else:
                self.centers = center_offset  # Keep original if max_dist is 0

    def perform_fast_marching(
        self, speed_map: np.ndarray, seed_points: np.ndarray
    ) -> np.ndarray:
        """
        Perform fast marching using scikit-fmm

        Parameters
        ----------
        speed_map : np.ndarray
            Speed map (inverse of density map from MATLAB code)
        seed_points : np.ndarray
            Seed points as 2xN array where each column is a point [x, y]
            Using 1-based indexing like MATLAB

        Returns
        -------
        np.ndarray
            Distance map from seed points
        """
        try:
            # Create a mask for valid regions (inside the circular domain)
            valid_mask = speed_map > 1e-10

            # Create a signed distance function for the domain
            # Initialize with large positive values (far from boundary)
            phi = np.ones_like(speed_map) * 1000.0

            # Set seed points to 0 (starting points for Fast Marching)
            for i in range(seed_points.shape[1]):
                # MATLAB uses 1-based indexing, convert to 0-based for Python
                x, y = int(seed_points[0, i] - 1), int(seed_points[1, i] - 1)
                # Ensure indices are within bounds
                if 0 <= x < speed_map.shape[0] and 0 <= y < speed_map.shape[1]:
                    phi[x, y] = 0.0  # Starting points

            # Set invalid regions (outside circle) to negative values
            phi[~valid_mask] = -1000.0

            # Use scikit-fmm to solve the Eikonal equation
            # This is equivalent to MATLAB's perform_fast_marching
            distance = skfmm.distance(phi, dx=1.0)

            # Set invalid regions to very small values so they won't be selected
            distance[~valid_mask] = -1e10

            return distance

        except Exception as e:
            # Fallback to simple Euclidean distance if scikit-fmm fails
            print(f"Warning: scikit-fmm failed ({e}), using fallback method")
            return self._fallback_distance_calculation(speed_map, seed_points)

    def _fallback_distance_calculation(
        self, speed_map: np.ndarray, seed_points: np.ndarray
    ) -> np.ndarray:
        """
        Fallback distance calculation using simple Euclidean distance
        """
        n = speed_map.shape[0]
        D = np.full((n, n), -1e10)  # Initialize with very small values

        # Calculate Euclidean distance to nearest seed point
        for i in range(n):
            for j in range(n):
                if speed_map[i, j] > 1e-10:  # Only calculate for valid regions
                    min_dist = float("inf")
                    for k in range(seed_points.shape[1]):
                        # Convert from 1-based to 0-based indexing
                        dist = np.sqrt(
                            (i - (seed_points[0, k] - 1)) ** 2
                            + (j - (seed_points[1, k] - 1)) ** 2
                        )
                        min_dist = min(min_dist, dist)
                    D[i, j] = min_dist
        return D

    def generate_minfr(
        self,
        type: Literal["constant", "linear_rt", "linear_idx", "random"],
        par1: float | None = None,
        par2: float | None = None,
    ):
        """Generate minimal firing rates"""
        self.type = type
        self.minfr = self.generate_mu_minfr(self.rt / np.max(self.rt), type, par1, par2)
        self.minfr = self.minfr.flatten()

    def generate_mu_minfr(
        self,
        normalized_rt: np.ndarray,
        type: Literal["constant", "linear_rt", "linear_idx", "random"],
        par1: float | None = None,
        par2: float | None = None,
    ) -> np.ndarray:
        """
        Generate minimum firing rates for motor units

        This function takes recruitment thresholds and generates minimum
        firing rates according to the following model types:
        - 'constant': Fuglevand et al. 1993 - same minimum firing rate across all motor units,
           equal to 8 pulses/s or set by the user in 'par1'. If 'par1' is not provided, defaults to 8 pulses/s.
        - 'linear_rt': Linear recruitment threshold model, minimum firing rate = par2 + par1 * rt
        - 'linear_idx': Linear index model, minimum firing rate = par2 + par1 * (0:length(rt)-1)
        - 'random': Keenan and Valero-Cuevas 2007. Random model where minimum firing rate is linearly mapped
           between two random values from a uniform distribution between 6 and 12 pulses/s. The first value is the
           minimum firing rate of the smallest motor unit and the second is for the largest motor unit.

        Parameters
        ----------
        normalized_rt : np.ndarray
            Normalized recruitment thresholds
        type : Literal['constant', 'linear_rt', 'linear_idx', 'random']
            Type of firing rate model. See above for details.
        par1 : float | None
            First parameter (meaning depends on type)
        par2 : float | None
            Second parameter (meaning depends on type)

        Returns
        -------
        np.ndarray
            Minimum firing rates
        """
        match type:
            case "constant":
                if par1 is None:
                    par1 = 8
                mfr = np.ones_like(normalized_rt) * par1
            case "linear_rt":
                if par1 is None or par2 is None:
                    raise ValueError(
                        "par1 and par2 must be provided for linear_rt model"
                    )
                mfr = par2 + par1 * normalized_rt
            case "linear_idx":
                if par1 is None or par2 is None:
                    raise ValueError(
                        "par1 and par2 must be provided for linear_idx model"
                    )
                mfr = par2 + par1 * np.arange(len(normalized_rt))
            case "random":
                a = 5 + RANDOM_GENERATOR.integers(1, 8)
                b = 5 + RANDOM_GENERATOR.integers(1, 8)
                mfr = a + (b - a) / (len(normalized_rt) - 1) * np.arange(
                    len(normalized_rt)
                )
            case _:
                raise ValueError(f"Unknown firing rate type: {type}")

        return mfr

    def generate_maxfr(
        self,
        type: Literal["constant", "linear_rt", "linear_idx", "random"],
        par1: float | None = None,
        par2: float | None = None,
    ):
        """Generate maximal firing rates"""
        self.type = type
        self.maxfr = self.generate_mu_maxfr(self.rt / np.max(self.rt), type, par1, par2)
        self.maxfr = self.maxfr.flatten()

    def generate_mu_maxfr(
        self,
        normalized_rt: np.ndarray,
        type: Literal["constant", "linear_rt", "linear_idx", "random"],
        par1: float | None = None,
        par2: float | None = None,
    ) -> np.ndarray:
        """
        Generate maximum firing rates for motor units

        This function takes recruitment thresholds and generates maximum
        firing rates according to the following model types:
        - 'constant': Fuglevand et al. 1993 - same maximum firing rate across all motor units,
           equal to 25 pulses/s or set by the user in 'par1'. If 'par1' is not provided, defaults to 25 pulses/s.
        - 'linear_rt': Linear recruitment threshold model, maximum firing rate = par2 + par1 * rt
        - 'linear_idx': Linear index model, maximum firing rate = par2 + par1 * (0:length(rt)-1)
        - 'random': Keenan and Valero-Cuevas 2007. Random model where maximum firing rate is linearly mapped
           between two random values from a uniform distribution between 20 and 30 pulses/s. The first value is the
           maximum firing rate of the smallest motor unit and the second is for the largest motor unit.

        Parameters
        ----------
        normalized_rt : np.ndarray
            Normalized recruitment thresholds
        type : Literal['constant', 'linear_rt', 'linear_idx', 'random']
            Type of firing rate model. See above for details.
        par1 : float | None
            First parameter (meaning depends on type)
        par2 : float | None
            Second parameter (meaning depends on type)

        Returns
        -------
        np.ndarray
            Maximum firing rates
        """
        match type:
            case "constant":
                if par1 is None:
                    par1 = 25
                mfr = np.ones_like(normalized_rt) * par1
            case "linear_rt":
                if par1 is None or par2 is None:
                    raise ValueError(
                        "par1 and par2 must be provided for linear_rt model"
                    )
                mfr = par2 + par1 * normalized_rt
            case "linear_idx":
                if par1 is None or par2 is None:
                    raise ValueError(
                        "par1 and par2 must be provided for linear_idx model"
                    )
                mfr = par2 + par1 * np.arange(len(normalized_rt))
            case "random":
                # Random values from U(20,30) - using 20 + randi(10) equivalent
                a = 20 + RANDOM_GENERATOR.integers(
                    1, 11
                )  # randi(10) gives 1-10, so 20+1 to 20+10 = 21-30
                b = 20 + RANDOM_GENERATOR.integers(1, 11)
                mfr = a + (b - a) / (len(normalized_rt) - 1) * np.arange(
                    len(normalized_rt)
                )
            case _:
                raise ValueError(f"Unknown firing rate type: {type}")

        return mfr

    def generate_frs(
        self,
        type: Literal["constant", "linear_rt", "linear_idx", "random"],
        par1: float | None = None,
        par2: float | None = None,
    ):
        """Generate firing rate slopes"""
        self.type = type
        self.frs = self.generate_mu_fr_slope(
            self.rt / np.max(self.rt), type, par1, par2
        )
        self.frs = self.frs.flatten()

    def generate_mu_fr_slope(
        self,
        normalized_rt: np.ndarray,
        type: Literal["constant", "linear_rt", "linear_idx", "random"],
        par1: float | None = None,
        par2: float | None = None,
    ) -> np.ndarray:
        """
        Generate firing rate slopes for motor units

        This function takes recruitment thresholds and generates firing rate slopes
        according to the following model types:
        - 'constant': Fuglevand et al. 1993 - same firing rate slope across all motor units,
           equal to 8 [p/(s*excitation)] or set by the user in 'par1'. If 'par1' is not provided, defaults to 8.
        - 'linear_rt': Linear recruitment threshold model, firing rate slope = par2 + par1 * rt
        - 'linear_idx': Linear index model, firing rate slope = par2 + par1 * (0:length(rt)-1)
        - 'random': Random model where firing rate slope is linearly mapped between two random values
           from a uniform distribution between 6 and 12. The first value is the firing rate slope of the
           smallest motor unit and the second is for the largest motor unit.

        Parameters
        ----------
        normalized_rt : np.ndarray
            Normalized recruitment thresholds
        type : Literal['constant', 'linear_rt', 'linear_idx', 'random']
            Type of firing rate slope model. See above for details.
        par1 : float | None
            First parameter (meaning depends on type)
        par2 : float | None
            Second parameter (meaning depends on type)

        Returns
        -------
        np.ndarray
            Firing rate slopes
        """
        match type:
            case "constant":
                if par1 is None:
                    par1 = 8
                frs = np.zeros_like(normalized_rt) + par1
            case "linear_rt":
                if par1 is None or par2 is None:
                    raise ValueError(
                        "par1 and par2 must be provided for linear_rt model"
                    )
                frs = par2 + par1 * normalized_rt
            case "linear_idx":
                if par1 is None or par2 is None:
                    raise ValueError(
                        "par1 and par2 must be provided for linear_idx model"
                    )
                frs = par2 + par1 * np.arange(len(normalized_rt))
            case "random":
                # Random values from U(6,12) - using 5 + randi(7) equivalent
                a = 5 + RANDOM_GENERATOR.integers(
                    1, 8
                )  # randi(7) gives 1-7, so 5+1 to 5+7 = 6-12
                b = 5 + RANDOM_GENERATOR.integers(1, 8)
                frs = a + (b - a) / (len(normalized_rt) - 1) * np.arange(
                    len(normalized_rt)
                )
            case _:
                raise ValueError(f"Unknown firing rate slope type: {type}")

        return frs

    def set_deluca_mdl(self, type: Literal["fdi", "vl", "ta"]):
        """
        Alternative model of excitation-rate curves (DeLuca model)

        Parameters
        ----------
        type : Literal['fdi', 'vl', 'ta']
            Type of muscle:
            - 'fdi': first dorsal interosseus
            - 'vl': vastus lateralis
            - 'ta': tibialis anterior

        """
        match type:
            case "fdi":
                A, B, C, D, E = 85, 0.32, -23, 6.93, 20.9
            case "vl":
                A, B, C, D, E = 116, 0.15, -21, 8.03, 19.0
            case "ta":
                A, B, C, D, E = 65, 0.30, -30, 6.54, 20.2
            case _:
                raise ValueError(f"Unknown muscle type: {type}")

        self.excfr_pars = np.array([A, B, C, D, E])
        self.type = "deluca"

    def calculate_fr(
        self, excitation: Union[float, np.ndarray], m: Optional[int] = None
    ) -> np.ndarray:
        """
        Calculate firing rate for given excitation

        Parameters
        ----------
        excitation : Union[float, np.ndarray]
            Excitation level(s). If a single value is provided, it is converted to a column vector.
        m : Optional[int]
            Motor unit index (if None, calculate for all units)

        Returns
        -------
        np.ndarray
            Firing rates (columns: excitation; rows: motor neurons)
        """
        excitation = np.atleast_1d(excitation)

        if m is None:
            # Vectorized calculation for all motor units
            # Reshape for broadcasting: excitation (n_exc, 1), parameters (1, n_units)
            exc_reshaped = excitation.reshape(-1, 1)  # (n_excitation, 1)
            rt_reshaped = self.rt.reshape(1, -1)  # (1, n_units)

            if self.type == "deluca":
                A, B, C, D, E = self.excfr_pars
                fr = (
                    D * exc_reshaped
                    + (C - A * np.exp(-exc_reshaped / B)) * rt_reshaped
                    + E
                )
                fr[exc_reshaped <= rt_reshaped] = 0
            else:
                minfr_reshaped = self.minfr.reshape(1, -1)  # (1, n_units)
                maxfr_reshaped = self.maxfr.reshape(1, -1)  # (1, n_units)
                frs_reshaped = self.frs.reshape(1, -1)  # (1, n_units)

                fr = minfr_reshaped + np.maximum(
                    0, frs_reshaped * (exc_reshaped - rt_reshaped)
                )
                fr = np.minimum(maxfr_reshaped, fr)
                fr[exc_reshaped <= rt_reshaped] = 0
        else:
            # Single motor unit calculation
            if self.type == "deluca":
                A, B, C, D, E = self.excfr_pars
                fr = D * excitation + (C - A * np.exp(-excitation / B)) * self.rt[m] + E
                fr[excitation <= self.rt[m]] = 0
            else:
                fr = self.minfr[m] + np.maximum(
                    0, self.frs[m] * (excitation - self.rt[m])
                )
                fr = np.minimum(self.maxfr[m], fr)
                fr[excitation <= self.rt[m]] = 0

        return fr  # Shape: (n_excitation, n_units) for m=None, (n_excitation,) for specific m

    def generate_spike_train_gauss(
        self, T: np.ndarray, prev_state: np.ndarray, excitation: np.ndarray, fs: float
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        Generate spike trains using Gaussian IPI model (Based on Fuglevand 1993)

        Parameters
        ----------
        T : np.ndarray
            Time vector
        prev_state : np.ndarray
            Previous firing state
        excitation : np.ndarray
            Excitation signal
        fs : float
            Sampling frequency

        Returns
        -------
        Tuple[np.ndarray, np.ndarray, np.ndarray]
            spikes : np.ndarray
                Spike train matrix
            next_state : np.ndarray
                Next firing state
            last_ipi : np.ndarray
                Last inter-pulse intervals
        """
        spikes = np.zeros((len(T), self.N))
        ipi = np.zeros(self.N)
        next_firing = prev_state.copy()
        next_state = np.zeros(self.N)
        last_ipi = np.zeros(self.N)

        for m in range(self.N):
            for t in range(len(T)):
                if excitation[t] > self.rt[m]:
                    if np.isnan(next_firing[m]):
                        ipi[m] = fs / self.calculate_fr(excitation[t], m)
                        ipi[m] = ipi[m] + np.random.randn() * ipi[m] * self.CV
                        next_firing[m] = T[t] + round(ipi[m])

                    if T[t] == next_firing[m]:
                        spikes[t, m] = 1
                        ipi[m] = fs / self.calculate_fr(excitation[t], m)
                        ipi[m] = ipi[m] + np.random.randn() * ipi[m] * self.CV
                        next_firing[m] = T[t] + round(ipi[m])
                else:
                    next_firing[m] = np.nan

            last_ipi[m] = ipi[m]
            next_state[m] = next_firing[m]

        return spikes, next_state, last_ipi

    def show_sizes(self, ax: Optional[plt.Axes] = None):
        """Show motor unit sizes and recruitment thresholds"""
        if ax is None:
            fig, ax = plt.subplots()

        ax.clear()

        # Primary y-axis for sizes
        line1 = ax.stem(range(1, self.N + 1), self.sz, label="MU sizes")
        ax.set_xlabel("Index of MU")
        ax.set_ylabel("Motor neuron size, normalized units")
        ax.set_title("Thresholds and sizes distribution over the motor neuron pool")

        # Secondary y-axis for thresholds
        ax2 = ax.twinx()
        line2 = ax2.stem(
            range(1, self.N + 1),
            self.rt,
            linefmt="C1-",
            markerfmt="C1o",
            basefmt="C1-",
            label="Recruitment thresholds",
        )
        ax2.set_ylabel("Excitation rate, normalized units")

        # Combine legends
        lines = [line1, line2]
        labels = [line.get_label() for line in lines]
        ax.legend(lines, labels, loc="upper left")

    def show_centers(self, ax: Optional[plt.Axes] = None, Rmuscle: float = 10.0):
        """Show motor unit innervation centers and areas"""
        if ax is None:
            fig, ax = plt.subplots(figsize=(10, 10))

        ax.clear()

        # Muscle border generation
        phi_circle = np.linspace(0, 2 * np.pi, 1000)
        muscle_border = np.column_stack(
            [Rmuscle * np.cos(phi_circle), Rmuscle * np.sin(phi_circle)]
        )
        ax.plot(muscle_border[:, 0], muscle_border[:, 1], "k", linewidth=1)

        # Check if centers exist and have the right shape
        if self.centers is not None and len(self.centers) > 0:
            for i in range(len(self.centers)):
                ax.text(self.centers[i, 0] - 0.1, self.centers[i, 1], str(i + 1))
                rad = np.sqrt(self.sz[i] / np.sum(self.sz) * np.pi * Rmuscle**2 / np.pi)
                mu_area_circle = np.column_stack(
                    [
                        rad * np.cos(phi_circle) + self.centers[i, 0],
                        rad * np.sin(phi_circle) + self.centers[i, 1],
                    ]
                )
                ax.plot(mu_area_circle[:, 0], mu_area_circle[:, 1], "b", linewidth=0.5)

        ax.set_aspect("equal")
        ax.set_xlabel("x, mm")
        ax.set_ylabel("y, mm")

    def generate_exc_fr_curves(self):
        """Generate excitation-firing rate curves"""
        excitation = np.linspace(0, 1, 1000)
        self.exc_fr_curves = self.calculate_fr(excitation)

    def show_fr_exc_curves(
        self, ax: Optional[plt.Axes] = None, inds: Optional[np.ndarray] = None
    ):
        """Show firing rate vs excitation curves"""
        if ax is None:
            fig, ax = plt.subplots()

        if inds is None:
            inds = np.arange(self.N)

        ax.clear()

        self.generate_exc_fr_curves()
        exc = np.linspace(0, 1, self.exc_fr_curves.shape[0])

        lines = ax.plot(exc, self.exc_fr_curves[:, inds], "k", linewidth=0.5)
        ax.set_xlabel("Excitation, normalized")
        ax.set_ylabel("Firing rates, pulses per second")

        return lines
