import numpy as np


def get_mf_dist_parameters(innervation_areas):
    """
    Get muscle fiber distribution parameters

    Parameters:
    -----------
    innervation_areas : array_like
        Innervation areas for each motor unit

    Returns:
    --------
    tuple
        (diam_means, diam_stds) - means and standard deviations of fiber diameters
    """
    std_d = 9e-6  # 9 um, value taken from HAMILTON-WRIGHT AND STASHUK:
    # PHYSIOLOGICALLY BASED SIMULATION OF CLINICAL EMG SIGNALS
    mean_d = 55e-6  # 55 um, taken from the same source
    cv = std_d / mean_d

    innervation_areas = innervation_areas / np.sum(innervation_areas)
    cumul_ia = np.cumsum(innervation_areas)
    Nmu = len(innervation_areas)

    diam_means = np.zeros(Nmu)
    diam_stds = np.zeros(Nmu)

    # A method by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> (PHYSIOLOGICALLY BASED SIMULATION OF CLINICAL EMG SIGNALS)
    for i in range(Nmu):
        # Original function worked bad for large number of MUs, so using cumulative sum
        diam_means[i] = mean_d - std_d + cumul_ia[i] * 2 * std_d
        diam_stds[i] = diam_means[i] * cv  # See eq. 11

    return diam_means, diam_stds


def assign_mf_cv(
    diameters: np.ndarray,
    max_cv: float = 5200,
    intercept: float = 2200,
    min_diameter: float = 22e-6,
    max_diameter: float = 85e-6,
):
    """
    Assign conduction velocities to muscle fibers based on their diameters.
    Default values are taken from Stashuk and Hamilton-Wright (Simulation of single muscle fibre action potentials by)

    Parameters:
    -----------
    diameters : np.ndarray
        Fiber diameters in meters
    max_cv : float, optional
        Maximum conduction velocity in mm/s. By default, the maximum conduction
        velocity is 5200 mm/s.
    intercept : float, optional
        Intercept for CV calculation in mm/s. By default, the intercept is 2200 mm/s.
    min_diameter : float, optional
        Minimum diameter in meters. By default, the minimum diameter is 22e-6 m.
    max_diameter : float, optional
        Maximum diameter in meters. By default, the maximum diameter is 85e-6 m.

    Returns:
    --------
    np.ndarray
        Conduction velocities in mm/s
    """
    diameters = np.clip(diameters, min_diameter, max_diameter)

    slope = (max_cv - intercept) / (max_diameter - min_diameter)  # m/s/um

    return intercept + (diameters - min_diameter) * slope
