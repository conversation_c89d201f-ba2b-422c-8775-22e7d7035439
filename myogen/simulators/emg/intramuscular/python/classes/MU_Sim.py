"""
Motor Unit Simulation class for handling individual motor unit simulations
and MUAP (Motor Unit Action Potential) calculations.

Translated from MATLAB MU_Sim class functionality.
"""

import numpy as np
from typing import Optional, Tuple
from myogen.utils import RANDOM_GENERATOR


class MU_Sim:
    """
    Motor Unit Simulation class for handling individual motor unit simulations
    and MUAP calculations.

    This class represents a single motor unit with its muscle fibers,
    neuromuscular junctions, and action potential calculations.
    """

    def __init__(
        self,
        mf_centers: np.ndarray,
        mf_index: int,
        Lmuscle: float,
        mf_diameters: np.ndarray,
        mf_cv: np.ndarray,
        branch_v: np.ndarray,
        nominal_center: Optional[np.ndarray] = None,
    ):
        """
        Initialize a Motor Unit Simulation object.

        Parameters
        ----------
        mf_centers : np.ndarray
            Muscle fiber center coordinates (Nx2 array)
        mf_index : int
            Motor unit index (unused in current implementation, kept for compatibility)
        Lmuscle : float
            Muscle length in mm
        mf_diameters : np.ndarray
            Muscle fiber diameters
        mf_cv : np.ndarray
            Muscle fiber conduction velocities
        branch_v : np.ndarray
            NMJ branch conduction velocities [mm/s] - [axon_v, branch_v]
        nominal_center : np.ndarray, optional
            Nominal center of the motor unit
        """
        self.mf_centers = mf_centers
        self.Lmuscle = Lmuscle
        self.mf_diameters = mf_diameters
        self.mf_cv = mf_cv
        self.branch_v = branch_v
        self.nominal_center = nominal_center

        self.n_fibers = len(mf_centers)

        # NMJ (neuromuscular junction) properties
        self.nmj_x = None
        self.nmj_y = None
        self.nmj_z = None
        self.nerve_paths = None
        self.mnap_delays = None

        # SFAP and MUAP storage
        self.sfaps = None  # Single fiber action potentials
        self.muap = None  # Motor unit action potential
        self.muap_original = None  # Original MUAP before differential recording
        self.dt = None  # Sampling time interval (stored from calc_sfaps)

    def sim_nmj_branches_two_layers(
        self,
        n_branches: int,
        endplate_area_center: float,
        branches_z_std: float,
        arborization_z_std: float,
    ):
        """
        Simulate neuromuscular junction branches using a two-layer model.

        Parameters
        ----------
        n_branches : int
            Number of branches
        endplate_area_center : float
            Center of endplate area along z-axis
        branches_z_std : float
            Standard deviation for branch distribution
        arborization_z_std : float
            Standard deviation for arborization distribution
        """
        # Generate branch points along z-axis
        branch_z = RANDOM_GENERATOR.normal(
            endplate_area_center, branches_z_std, n_branches
        )

        # For each muscle fiber, assign to nearest branch and add arborization
        self.nmj_z = np.zeros(self.n_fibers)
        self.nmj_x = self.mf_centers[:, 0].copy()
        self.nmj_y = self.mf_centers[:, 1].copy()

        for i in range(self.n_fibers):
            # Find nearest branch
            distances = np.abs(branch_z - endplate_area_center)
            nearest_branch_idx = np.argmin(distances)

            # Add arborization variability
            self.nmj_z[i] = branch_z[nearest_branch_idx] + RANDOM_GENERATOR.normal(
                0, arborization_z_std
            )

        # Calculate nerve paths (simplified - distance from center to NMJ)
        self.nerve_paths = np.zeros((self.n_fibers, 1))
        if self.nominal_center is not None:
            center_z = endplate_area_center  # Assume center is at endplate area
            for i in range(self.n_fibers):
                # Distance from nominal center to NMJ
                dx = self.nmj_x[i] - self.nominal_center[0]
                dy = self.nmj_y[i] - self.nominal_center[1]
                dz = self.nmj_z[i] - center_z
                self.nerve_paths[i, 0] = np.sqrt(dx**2 + dy**2 + dz**2)
        else:
            # If no nominal center, use distance from muscle center
            for i in range(self.n_fibers):
                dz = self.nmj_z[i] - endplate_area_center
                self.nerve_paths[i, 0] = np.abs(dz)

        # Calculate propagation delays (time for signal to reach NMJ)
        # Using axon velocity (first element of branch_v)
        axon_velocity = self.branch_v[0]  # mm/s
        self.mnap_delays = self.nerve_paths[:, 0] / axon_velocity  # seconds

    def calc_sfaps(
        self,
        dt: float,
        dz: float,
        electrode_pts: np.ndarray,
        electrode_normals: Optional[np.ndarray] = None,
    ):
        """
        Calculate single fiber action potentials (SFAPs).

        Parameters
        ----------
        dt : float
            Time sampling interval [s]
        dz : float
            Spatial sampling interval [mm]
        electrode_pts : np.ndarray
            Electrode point coordinates (N x 3 array)
        electrode_normals : np.ndarray, optional
            Electrode normal vectors (N x 3 array)
        """
        n_electrode_pts = electrode_pts.shape[0]

        # Store the sampling interval for use in calc_muap
        self.dt = dt

        # Check if this motor unit has any fibers
        if self.n_fibers == 0 or len(self.mf_cv) == 0:
            # Create empty SFAP array for motor units with no fibers
            self.sfaps = np.zeros((100, n_electrode_pts, 0))  # Default 100 time samples
            return

        # Simplified SFAP calculation
        # In practice, this would involve complex bioelectric field calculations
        # Here we use a simplified model based on distance and conduction velocity

        # Estimate SFAP duration based on fiber properties
        max_cv = np.max(self.mf_cv)
        fiber_length = self.Lmuscle  # Assume fibers span muscle length
        propagation_time = fiber_length / max_cv  # seconds

        # SFAP duration is typically 2-3 times the propagation time
        sfap_duration = 3 * propagation_time
        n_time_samples = int(np.ceil(sfap_duration / dt))

        # Initialize SFAP storage
        self.sfaps = np.zeros((n_time_samples, n_electrode_pts, self.n_fibers))

        # Time vector
        t = np.arange(n_time_samples) * dt

        for fiber_idx in range(self.n_fibers):
            fiber_cv = self.mf_cv[fiber_idx]
            fiber_center = self.mf_centers[fiber_idx, :]
            nmj_z = self.nmj_z[fiber_idx]

            # Simple SFAP model: biphasic waveform
            # The actual implementation would use more sophisticated models
            for pt_idx in range(n_electrode_pts):
                electrode_pos = electrode_pts[pt_idx, :]

                # Distance from fiber to electrode point
                dx = fiber_center[0] - electrode_pos[0]
                dy = fiber_center[1] - electrode_pos[1]
                dz_electrode = electrode_pos[2]

                # Distance varies along fiber length
                distances = np.sqrt(
                    dx**2 + dy**2 + (dz_electrode - (nmj_z + t * fiber_cv)) ** 2
                )

                # Avoid division by zero
                distances = np.maximum(distances, 0.01)  # 0.01 mm minimum distance

                # Simple current dipole model
                # SFAP amplitude is inversely related to distance
                amplitude_factor = 1.0 / distances**2

                # Biphasic waveform (simplified)
                # First derivative of Gaussian
                sigma = sfap_duration / 6  # Standard deviation
                gaussian = np.exp(-((t - sfap_duration / 2) ** 2) / (2 * sigma**2))
                dgaussian_dt = -(t - sfap_duration / 2) / sigma**2 * gaussian

                # Scale by amplitude factor (using mean for simplicity)
                mean_amplitude = np.mean(amplitude_factor)
                self.sfaps[:, pt_idx, fiber_idx] = (
                    dgaussian_dt * mean_amplitude * 1e-3
                )  # Scale to millivolts for visibility

    def calc_muap(self, jitter_std: float = 0.0) -> np.ndarray:
        """
        Calculate Motor Unit Action Potential (MUAP) by summing SFAPs.

        Parameters
        ----------
        jitter_std : float, optional
            Standard deviation of jitter in seconds (default: 0.0 for no jitter)

        Returns
        -------
        np.ndarray
            MUAP waveform
        """
        if self.sfaps is None:
            raise ValueError("SFAPs must be calculated first using calc_sfaps()")

        n_time_samples, n_electrode_pts, n_fibers = self.sfaps.shape

        # Initialize MUAP
        muap = np.zeros((n_time_samples, n_electrode_pts))

        # If no fibers, return zero MUAP
        if n_fibers == 0:
            self.muap = muap
            return muap

        if jitter_std > 0:
            # Apply jitter to each fiber
            dt = self.dt if self.dt is not None else 1e-4  # Use stored dt or fallback
            jitter_samples = RANDOM_GENERATOR.normal(
                0, jitter_std / dt, n_fibers
            ).astype(int)

            for fiber_idx in range(n_fibers):
                jitter = jitter_samples[fiber_idx]
                delay = int(self.mnap_delays[fiber_idx] / dt) + jitter

                # Add SFAP with delay and jitter
                if 0 <= delay < n_time_samples:
                    end_idx = min(n_time_samples, n_time_samples - delay)
                    muap[delay : delay + end_idx, :] += self.sfaps[
                        :end_idx, :, fiber_idx
                    ]
        else:
            # No jitter - simple summation with propagation delays
            dt = self.dt if self.dt is not None else 1e-4  # Use stored dt or fallback

            for fiber_idx in range(n_fibers):
                delay = int(self.mnap_delays[fiber_idx] / dt)

                # Add SFAP with delay
                if 0 <= delay < n_time_samples:
                    end_idx = min(n_time_samples, n_time_samples - delay)
                    muap[delay : delay + end_idx, :] += self.sfaps[
                        :end_idx, :, fiber_idx
                    ]

        self.muap = muap
        return muap

    def get_elementary_current_response(
        self, t: float, z: float, r: np.ndarray
    ) -> np.ndarray:
        """
        Calculate elementary current response (simplified model).

        Parameters
        ----------
        t : float
            Time
        z : float
            Z coordinate
        r : np.ndarray
            Radial distances

        Returns
        -------
        np.ndarray
            Current response amplitudes
        """
        # Simplified current dipole model
        # In practice, this would use more sophisticated bioelectric field calculations
        r = np.maximum(r, 0.055)  # Minimum distance 55 µm
        return 1.0 / (4 * np.pi * r**2)  # Basic current dipole response
