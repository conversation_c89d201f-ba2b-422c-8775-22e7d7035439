from typing import Literal
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import integrate
from scipy.stats import chi2, multivariate_normal
from scipy.spatial.distance import cdist
from sklearn.neighbors import NearestNeighbors
from scipy.spatial import ConvexHull
from pathlib import Path

from myogen.utils import RANDOM_GENERATOR
from myogen.simulators.emg.intramuscular.python.classes.muscle_functions import (
    get_mf_dist_parameters,
    assign_mf_cv,
)


class MU_Pool_Sim:
    """
    Motor Unit Pool Simulation class

    This class simulates a motor unit pool with muscle fibers,
    innervation areas, and assignment of fibers to motor neurons.
    """

    def __init__(self, mn_pool):
        """
        Initialize the MU_Pool_Sim object

        Parameters:
        -----------
        mn_pool : object
            Motor neuron pool object containing N (number of neurons) and other properties
        """
        self.mn_pool = mn_pool
        self.N = mn_pool.N

        # Initialize properties
        self.innervation_areas = None
        self.innervation_areas_res = None
        self.muscle_area2max_ia = None
        self.innervation_numbers = None
        self.innervation_numbers_res = None
        self.mf_centers = None
        self.Nmf = None
        self.Dmf = None
        self.Rmuscle = None
        self.muscle_border = None
        self.mf_diameters = None
        self.mf_cv = None
        self.assignment = None

    def generate_mfs(self, Rmuscle: float, Dmf: float = 400):
        """
        Generate muscle fiber centers

        Parameters:
        -----------
        Rmuscle : float
            Muscle radius in mm
        Dmf: float, optional
            Density of muscle fibers per square millimetre (default: 400)
        """
        self.Dmf = Dmf
        self.Rmuscle = Rmuscle

        # Expected number of muscle fibers in the muscle
        self.Nmf = int(np.rint((Rmuscle**2) * np.pi * Dmf))

        # Get the class file path for precalculated data
        cls_path = Path(__file__).parent
        data_path = cls_path / "mf_data" / "voronoi_pi1e5.csv"

        self.mf_centers = pd.read_csv(data_path, header=None).values

        # Adjust the loaded centers to the expected number of fibers and muscle radius
        self.mf_centers = (self.mf_centers - 5) / 4  # 4 may be unnecessary here
        dists = np.sqrt(self.mf_centers[:, 0] ** 2 + self.mf_centers[:, 1] ** 2)
        sorted_indices = np.argsort(dists)

        if len(sorted_indices) >= self.Nmf + 1:
            self.mf_centers = (
                self.mf_centers[sorted_indices[: self.Nmf], :]
                / dists[sorted_indices[self.Nmf]]
                * Rmuscle
            )
        else:
            self.mf_centers = (
                self.mf_centers[sorted_indices, :] / dists[sorted_indices[-1]] * Rmuscle
            )
            self.Nmf = len(self.mf_centers)

        # Create muscle border for plotting
        phi_circle = np.linspace(0, 2 * np.pi, 1000)
        phi_circle = phi_circle[:-1]
        self.muscle_border = np.column_stack(
            [self.Rmuscle * np.cos(phi_circle), self.Rmuscle * np.sin(phi_circle)]
        )

    def calc_innervation_areas(self, muscle_area2max_ia=4):
        """
        Calculate innervation areas for motor units

        Parameters:
        -----------
        muscle_area2max_ia : float, optional
            Ratio of muscle area to maximum innervation area (default: 4)

        Returns:
        --------
        float
            Overlap degree
        """
        self.muscle_area2max_ia = muscle_area2max_ia

        muscle_area = np.pi * self.Rmuscle**2
        # Innervation areas are proportional to the sizes of MNs
        # Areas are scaled such that the largest MN innervates
        # 1/max_ia2muscle_area of the total muscle area
        self.innervation_areas = (
            self.mn_pool.sz / np.max(self.mn_pool.sz) * muscle_area / muscle_area2max_ia
        )

        return np.sum(self.innervation_areas) / muscle_area

    def calc_innervation_numbers(self):
        """Calculate target innervation numbers for each motor unit"""
        muscle_area = np.pi * self.Rmuscle**2
        self.innervation_numbers = np.round(
            self.innervation_areas
            / np.sum(self.innervation_areas)
            * muscle_area
            * self.Dmf
        ).astype(int)

    def assign_mfs2mns(
        self,
        n_neighbours: int = 3,
        conf: float = 0.999,
        fast_mode: bool = False,
        use_quad_vec: bool = False,
        use_qmc_quad: bool = False,
    ):
        """
        Assign muscle fibers to motor neurons according to several rules:
        - Proximity to the innervation center.
        - Innervation area and target number of innervated fibers.
        - Self-avoiding phenomena (see n_neighbours variable).

        Parameters:
        -----------
        n_neighbours : int, optional
            Number of neighbours for self-avoiding phenomena. By default,
            the number of neighbours is 3.
        conf : float, optional
            The confidence interval defines
            the ratio between the innervation area and the variance of the gaussian
            distribution. The larger, the tighter the clusters are. By default,
            the confidence interval is 0.999.
        fast_mode : bool, optional
            If True, skips the expensive out-of-circle compensation calculation
            for much faster execution. Default is False for maximum accuracy.
        use_quad_vec : bool, optional
            If True, uses scipy.integrate.quad_vec for vectorized integration
            instead of Monte Carlo. Requires polar coordinate conversion.
        use_qmc_quad : bool, optional
            If True, uses scipy.integrate.qmc_quad for Quasi-Monte Carlo
            integration. Best for 2D integration problems.
        """

        c = chi2.ppf(conf, 2)

        def sigma(ia):
            return np.eye(2) * ia / np.pi / c

        if fast_mode:
            # FAST MODE: Skip expensive out-of-circle compensation
            print("Using fast mode - skipping out-of-circle compensation")
            out_circle_coeff = np.ones(self.N)

        elif use_qmc_quad:
            # OPTIMAL: Use qmc_quad for 2D Quasi-Monte Carlo integration
            print("Using qmc_quad for 2D Quasi-Monte Carlo integration")
            from scipy.integrate import qmc_quad
            from scipy.stats.qmc import Halton

            out_circle_coeff = np.ones(self.N)

            # Integration bounds: square domain [-R, R] x [-R, R]
            a = np.array([-self.Rmuscle, -self.Rmuscle])
            b = np.array([self.Rmuscle, self.Rmuscle])

            # Create QMC engine for 2D integration
            qrng = Halton(d=2, scramble=True, seed=RANDOM_GENERATOR)

            def integrand_2d_vectorized(points):
                """
                Vectorized 2D integrand for all motor units simultaneously
                points: shape (2, n_points) where first row is x, second row is y
                returns: shape (n_points, N) where N is number of motor units
                """
                x_coords = points[0, :]  # shape (n_points,)
                y_coords = points[1, :]  # shape (n_points,)
                n_points = len(x_coords)

                # Check which points are inside the circular muscle
                distances_sq = x_coords**2 + y_coords**2
                inside_circle = distances_sq <= self.Rmuscle**2

                # Initialize result array: (n_points, N_motor_units)
                results = np.zeros((n_points, self.N))

                if not np.any(inside_circle):
                    return results  # All zeros if no points inside circle

                # Only compute for points inside the circle
                inside_indices = np.where(inside_circle)[0]
                x_inside = x_coords[inside_indices]
                y_inside = y_coords[inside_indices]
                points_inside = np.column_stack(
                    [x_inside, y_inside]
                )  # shape (n_inside, 2)

                # Vectorized computation for all motor units
                for mu in range(self.N):
                    cov_matrix = sigma(self.innervation_areas[mu])
                    mean = self.mn_pool.centers[mu, :]

                    # Compute multivariate normal PDF for all inside points
                    diff = points_inside - mean  # shape (n_inside, 2)
                    inv_cov = np.linalg.inv(cov_matrix)

                    # Vectorized Mahalanobis distance
                    mahalanobis_sq = np.sum(
                        (diff @ inv_cov) * diff, axis=1
                    )  # shape (n_inside,)

                    # Compute PDF values
                    det_cov = np.linalg.det(cov_matrix)
                    normalization = 1.0 / (2 * np.pi * np.sqrt(det_cov))
                    pdf_values = normalization * np.exp(-0.5 * mahalanobis_sq)

                    # Assign to results (only for inside points)
                    results[inside_indices, mu] = pdf_values

                return results

            # For qmc_quad, we need a function that returns a 1D array for each call
            # We'll integrate each motor unit separately but efficiently
            total_errors = []

            for mu in range(self.N):

                def integrand_single_mu(x):
                    """
                    Integrand for a single motor unit
                    x: array of shape (n_points, 2) where columns are [x_coord, y_coord]
                    """
                    if x.ndim == 1:
                        # Single point case: x is shape (2,) -> [x_coord, y_coord]
                        x_coord, y_coord = x[0], x[1]
                        x_coords = np.array([x_coord])
                        y_coords = np.array([y_coord])
                    else:
                        # Multiple points case: x is shape (n_points, 2)
                        x_coords = x[:, 0]
                        y_coords = x[:, 1]

                    # Check which points are inside the circular muscle
                    distances_sq = x_coords**2 + y_coords**2
                    inside_circle = distances_sq <= self.Rmuscle**2

                    result = np.zeros_like(x_coords)

                    if np.any(inside_circle):
                        inside_indices = np.where(inside_circle)[0]
                        x_inside = x_coords[inside_indices]
                        y_inside = y_coords[inside_indices]
                        points_inside = np.column_stack([x_inside, y_inside])

                        cov_matrix = sigma(self.innervation_areas[mu])
                        mean = self.mn_pool.centers[mu, :]

                        # Compute multivariate normal PDF
                        diff = points_inside - mean
                        inv_cov = np.linalg.inv(cov_matrix)
                        mahalanobis_sq = np.sum((diff @ inv_cov) * diff, axis=1)

                        det_cov = np.linalg.det(cov_matrix)
                        normalization = 1.0 / (2 * np.pi * np.sqrt(det_cov))
                        pdf_values = normalization * np.exp(-0.5 * mahalanobis_sq)

                        result[inside_indices] = pdf_values

                    # Return scalar for single point, array for multiple points
                    return result[0] if result.shape[0] == 1 else result

                # Perform QMC integration for this motor unit
                qrng_mu = Halton(
                    d=2, scramble=True, seed=RANDOM_GENERATOR.integers(0, 2**31)
                )
                integration_result = qmc_quad(
                    integrand_single_mu,
                    a,
                    b,
                    n_estimates=8,  # Number of independent QMC samples
                    n_points=2048,  # Points per sample (can be tuned)
                    qrng=qrng_mu,
                )

                integral_value = integration_result.integral
                error_estimate = integration_result.standard_error
                total_errors.append(error_estimate)

                if integral_value > 1e-10:
                    out_circle_coeff[mu] = 1.0 / integral_value
                else:
                    out_circle_coeff[mu] = 1.0

            max_error = np.max(total_errors)
            avg_error = np.mean(total_errors)
            print(
                f"qmc_quad integration completed - Max error: {max_error:.2e}, Avg error: {avg_error:.2e}"
            )

        elif use_quad_vec:
            # EXPERIMENTAL: Use quad_vec with polar coordinates
            print("Using quad_vec with polar coordinate integration")
            from scipy.integrate import quad_vec

            out_circle_coeff = np.ones(self.N)

            # For circular 2D Gaussian integration, we can convert to polar coordinates
            # ∫∫ f(x,y) dx dy = ∫₀^R ∫₀^2π f(r cos θ, r sin θ) r dr dθ
            # For circular Gaussian with covariance σI, the angular integral can be computed analytically

            def radial_integrand_vector(r):
                """Vector-valued function for radial integration over all motor units"""
                results = np.zeros(self.N)

                for mu in range(self.N):
                    # For isotropic Gaussian (σI), the angular integral over [0, 2π] gives 2π
                    # times the modified Bessel function I₀ for the cross terms, but since
                    # our covariance is diagonal (σI), we can use the simpler form

                    sigma_val = self.innervation_areas[mu] / np.pi / c
                    center = self.mn_pool.centers[mu, :]
                    center_dist = np.linalg.norm(center)

                    # For diagonal covariance matrix σI, the integral simplifies
                    # This is an approximation valid when the center is not too far from origin
                    if center_dist < 0.1 * self.Rmuscle:  # Center near origin
                        # Use simpler approximation
                        results[mu] = 2 * np.pi * r * np.exp(-(r**2) / (2 * sigma_val))
                    else:
                        # More complex case - use numerical approximation
                        # Average over angular positions (simplified)
                        angular_samples = 8
                        theta_vals = np.linspace(
                            0, 2 * np.pi, angular_samples, endpoint=False
                        )
                        angular_sum = 0

                        for theta in theta_vals:
                            x = r * np.cos(theta)
                            y = r * np.sin(theta)
                            point = np.array([x, y])
                            diff = point - center
                            mahalanobis_sq = np.sum(diff**2) / sigma_val
                            angular_sum += np.exp(-0.5 * mahalanobis_sq)

                        results[mu] = (
                            r
                            * angular_sum
                            * (2 * np.pi / angular_samples)
                            / (2 * np.pi * sigma_val)
                        )

                return results

            # Integrate from 0 to Rmuscle
            integral_result, error = quad_vec(
                radial_integrand_vector,
                0,
                self.Rmuscle,
                epsrel=1e-6,
                workers=1,  # Can be increased for parallel processing
            )

            # Set out-of-circle coefficients
            for mu in range(self.N):
                if integral_result[mu] > 1e-10:
                    out_circle_coeff[mu] = 1.0 / integral_result[mu]
                else:
                    out_circle_coeff[mu] = 1.0

            print(f"quad_vec integration completed with max error: {np.max(error):.2e}")

        else:
            # OPTIMIZATION: Use Monte Carlo integration instead of dblquad for speed
            # Generate random points within the circular muscle for Monte Carlo integration
            n_mc_samples = 10000  # Adjust this for accuracy vs speed trade-off

            # Generate random points in circle using rejection sampling (more uniform than polar)
            angles = RANDOM_GENERATOR.uniform(0, 2 * np.pi, n_mc_samples * 2)
            radii = (
                np.sqrt(RANDOM_GENERATOR.uniform(0, 1, n_mc_samples * 2)) * self.Rmuscle
            )
            mc_points = np.column_stack(
                [radii * np.cos(angles), radii * np.sin(angles)]
            )

            # Keep only points within the circle (should be all, but just to be safe)
            distances = np.sqrt(mc_points[:, 0] ** 2 + mc_points[:, 1] ** 2)
            mc_points = mc_points[distances <= self.Rmuscle][:n_mc_samples]

            muscle_area = np.pi * self.Rmuscle**2
            out_circle_coeff = np.ones(self.N)

            # Vectorized Monte Carlo integration for all motor units at once
            for mu in range(self.N):
                # Calculate multivariate normal PDF for all MC points at once
                cov_matrix = sigma(self.innervation_areas[mu])
                mean = self.mn_pool.centers[mu, :]

                # Vectorized PDF calculation
                diff = mc_points - mean
                inv_cov = np.linalg.inv(cov_matrix)

                # Compute (x-μ)ᵀ Σ⁻¹ (x-μ) for all points
                mahalanobis_sq = np.sum(diff @ inv_cov * diff, axis=1)

                # Compute PDF values
                det_cov = np.linalg.det(cov_matrix)
                normalization = 1.0 / (2 * np.pi * np.sqrt(det_cov))
                pdf_values = normalization * np.exp(-0.5 * mahalanobis_sq)

                # Monte Carlo estimate of integral
                in_circle_int = muscle_area * np.mean(pdf_values)
                out_circle_coeff[mu] = 1 / in_circle_int

        # Find nearest neighbors for suppression
        if n_neighbours > 0:
            nbrs = NearestNeighbors(n_neighbors=n_neighbours + 1).fit(self.mf_centers)
            _, neighbours = nbrs.kneighbors(self.mf_centers)
            neighbours = neighbours[:, 1:]  # Exclude self

        # OPTIMIZATION: Precompute sigma matrices and inverse covariances
        sigma_matrices = np.array([sigma(ia) for ia in self.innervation_areas])
        inv_sigma_matrices = np.array(
            [np.linalg.inv(sigma_matrices[mu]) for mu in range(self.N)]
        )
        det_sigma = np.array(
            [np.linalg.det(sigma_matrices[mu]) for mu in range(self.N)]
        )
        normalizations = 1.0 / (2 * np.pi * np.sqrt(det_sigma))

        # Assignment procedure
        self.assignment = np.full(self.Nmf, np.nan)
        randomized_mf = RANDOM_GENERATOR.permutation(self.Nmf)

        for i, mf in enumerate(randomized_mf):
            probs = np.zeros(self.N)

            # OPTIMIZATION: Vectorized probability calculation for all motor units
            fiber_pos = self.mf_centers[mf, :]

            # Check suppression for all motor units at once
            if n_neighbours > 0:
                neighbor_assignments = self.assignment[neighbours[mf]]
                neighbor_assignments = neighbor_assignments[
                    ~np.isnan(neighbor_assignments)
                ]
                suppressed_mus = set(neighbor_assignments.astype(int))
            else:
                suppressed_mus = set()

            for mu in range(self.N):
                if mu in suppressed_mus:
                    probs[mu] = 0
                else:
                    # A priori probability of assignment
                    apriori_prob = self.innervation_numbers[mu] / self.Nmf

                    # OPTIMIZATION: Use precomputed matrices for likelihood calculation
                    diff = fiber_pos - self.mn_pool.centers[mu, :]
                    mahalanobis_sq = diff @ inv_sigma_matrices[mu] @ diff
                    clust_hood = normalizations[mu] * np.exp(-0.5 * mahalanobis_sq)
                    clust_hood *= out_circle_coeff[mu]

                    # Final a posteriori probability
                    probs[mu] = apriori_prob * clust_hood

            # Normalize probabilities
            if np.sum(probs) > 0:
                probs = probs / np.sum(probs)
                self.assignment[mf] = RANDOM_GENERATOR.choice(self.N, p=probs)
            else:
                self.assignment[mf] = RANDOM_GENERATOR.choice(self.N)

            if (i + 1) % 1000 == 0:
                print(f"{i + 1} muscle fibers assigned")

        self.calc_innervation_numbers_res()
        self.calc_innervation_areas_res()

    def calc_innervation_numbers_res(self):
        """Calculate resulting innervation numbers"""
        self.innervation_numbers_res = np.bincount(
            self.assignment.astype(int), minlength=self.N
        )
        return self.innervation_numbers_res

    def calc_innervation_areas_res(
        self,
        area_type: Literal[
            "confidence_ellipse", "polygone_area"
        ] = "confidence_ellipse",
        conf=0.95,
    ):
        """
        Calculate resulting innervation areas

        Parameters:
        -----------
        area_type : Literal["confidence_ellipse", "polygone_area"], optional
            Type of area calculation ('confidence_ellipse' or 'polygone_area').
            By default, the area is calculated using the confidence ellipse.
        conf : float, optional
            Confidence level for ellipse calculation. By default, the confidence
            level is 0.95.
        """
        self.innervation_areas_res = np.zeros(self.N)

        match area_type:
            case "confidence_ellipse":
                for m in range(self.N):
                    fiber_indices = np.where(self.assignment == m)[0]
                    if len(fiber_indices) > 1:
                        covariance = np.cov(self.mf_centers[fiber_indices, :].T)
                        eigenvals = np.linalg.eigvals(covariance)

                        chisquare_val = chi2.ppf(conf, 2)
                        # Innervation area is area of the confidence interval ellipse
                        self.innervation_areas_res[m] = (
                            np.prod(np.sqrt(eigenvals)) * chisquare_val * np.pi
                        )
                    else:
                        self.innervation_areas_res[m] = 0

            case "polygone_area":
                for m in range(self.N):
                    fiber_indices = np.where(self.assignment == m)[0]
                    if len(fiber_indices) > 2:
                        points = self.mf_centers[fiber_indices, :]
                        hull = ConvexHull(points)
                        self.innervation_areas_res[m] = (
                            hull.volume
                        )  # In 2D, volume is area
                    else:
                        self.innervation_areas_res[m] = 0

        return self.innervation_areas_res

    def generate_mf_diameters(self):
        """Generate muscle fiber diameters"""
        diam_means, diam_stds = get_mf_dist_parameters(self.innervation_areas)

        # Theoretical diameters of muscle fibers (distribution parameters)
        # A method by Stashuk and Hamilton-Wright (PHYSIOLOGICALLY BASED SIMULATION OF CLINICAL EMG SIGNALS)
        self.mf_diameters = np.zeros(len(self.assignment))
        for m in range(self.N):
            fibers_in_mu = np.where(self.assignment == m)[0]
            self.mf_diameters[fibers_in_mu] = diam_means[m] + diam_stds[
                m
            ] * RANDOM_GENERATOR.standard_normal(len(fibers_in_mu))

    def generate_mf_cvs(self):
        """Assign muscle fiber conduction velocities"""
        self.mf_cv = assign_mf_cv(self.mf_diameters)

    # Visualization methods
    def show_mf_centers(self, ax=None):
        """Show muscle fiber centers"""
        if ax is None:
            fig, ax = plt.subplots(figsize=(8, 8))

        # Draw muscle border
        ax.plot(self.muscle_border[:, 0], self.muscle_border[:, 1], "k-", linewidth=2)
        ax.plot(self.mf_centers[:, 0], self.mf_centers[:, 1], "k.", markersize=1)

        ax.set_aspect("equal")
        ax.set_xlabel("X (mm)")
        ax.set_ylabel("Y (mm)")
        ax.set_title("Muscle fibers' centers in muscle cross-section")

        return ax

    def show_innervation_areas_1d(self, ax=None):
        """Show 1D plot of innervation areas"""
        if ax is None:
            fig, ax = plt.subplots(figsize=(10, 6))

        x = np.arange(1, self.N + 1)
        ax.plot(x, self.innervation_areas, "r-", linewidth=1.5, label="Target")
        ax.bar(x, self.innervation_areas_res, alpha=0.7, label="Generated")

        ax.set_xlim(0.5, self.N + 0.5)
        ax.set_xlabel("Motor neuron")
        ax.set_ylabel("Innervation areas (mm²)")
        ax.legend(loc="upper left")

        return ax

    def show_innervation_areas_2d(self, inds: np.ndarray = None, ax: plt.Axes = None):
        """Show 2D plot of innervation areas

        Parameters:
        -----------
        inds : np.ndarray, optional
            Indices of motor neurons to plot. By default, the first 10 motor
            neurons are plotted.
        ax : plt.Axes, optional
            Axes to plot on. By default, a new figure is created.
        """
        if inds is None:
            inds = np.round(np.linspace(0, self.N - 1, min(10, self.N))).astype(int)
        if ax is None:
            _, ax = plt.subplots(figsize=(10, 10))

        # Draw muscle border
        ax.plot(self.muscle_border[:, 0], self.muscle_border[:, 1], "k-", linewidth=2)

        colors = plt.cm.rainbow(np.linspace(0, 1, len(inds)))

        # shuffle colors
        colors = colors[RANDOM_GENERATOR.permutation(len(colors))]

        for i, m in enumerate(inds):
            fiber_indices = np.where(self.assignment == m)[0]
            if len(fiber_indices) > 0:
                points = self.mf_centers[fiber_indices, :]

                if len(fiber_indices) > 2:
                    hull = ConvexHull(points)
                    hull_points = points[hull.vertices]
                    hull_points = np.vstack(
                        [hull_points, hull_points[0]]
                    )  # Close the polygon
                    ax.plot(*hull_points.T, color=colors[i], linewidth=1.5, alpha=0.5)

                ax.plot(*points.T, ".", color=colors[i], markersize=2, alpha=0.5)

        ax.set_aspect("equal")
        ax.set_xlabel("x (mm)")
        ax.set_ylabel("y (mm)")
        ax.set_title("Motor neuron innervation areas over muscle cross-section")

        return ax

    def show_innervation_numbers(self, ax=None):
        """Show innervation numbers histogram"""
        if ax is None:
            fig, ax = plt.subplots(figsize=(10, 6))

        x = np.arange(1, self.N + 1)
        ax.bar(x, self.innervation_numbers_res, alpha=0.7, label="Generated")
        ax.plot(x, self.innervation_numbers, "r-", linewidth=1.5, label="Target")

        ax.set_xlim(0.5, self.N + 0.5)
        ax.set_xlabel("Motor neuron")
        ax.set_ylabel("Number of fibers")
        ax.legend(loc="upper left")

        return ax

    def show_cv_distribution(self, ax=None):
        """Show conduction velocity distribution"""
        if ax is None:
            fig, ax = plt.subplots(figsize=(8, 6))

        ax.hist(self.mf_cv, bins=30, alpha=0.7)
        ax.set_xlabel("Conduction velocity (mm/s)")
        ax.set_ylabel("Frequency")
        ax.set_title("Global distribution of MF conduction velocities")

        return ax

    def show_diameters_distribution(self, ax=None):
        """Show diameter distribution"""
        if ax is None:
            fig, ax = plt.subplots(figsize=(8, 6))

        diameters_um = self.mf_diameters * 1e6  # Convert to micrometers

        ax.hist(diameters_um, bins=30, alpha=0.7, label="Resulting distribution")

        # Theoretical normal distribution
        x_norm = np.linspace(np.min(diameters_um), np.max(diameters_um), 100)
        mu_theory = 55
        sigma_theory = 9

        # Scale the theoretical curve to match histogram
        hist_counts, _ = np.histogram(diameters_um, bins=30)
        scale_factor = np.sqrt(2 * np.pi) * 9 * np.max(hist_counts)
        y_norm = (
            scale_factor
            * (1 / (sigma_theory * np.sqrt(2 * np.pi)))
            * np.exp(-0.5 * ((x_norm - mu_theory) / sigma_theory) ** 2)
        )

        ax.plot(x_norm, y_norm, "r-", linewidth=2, label="Experimental distribution")

        # Add statistics text
        mu_hat = np.mean(diameters_um)
        sigma_hat = np.std(diameters_um)
        ax.text(
            0.7 * np.max(diameters_um),
            0.8 * np.max(hist_counts),
            f"μ̂ = {mu_hat:.1f}",
            fontsize=12,
        )
        ax.text(
            0.7 * np.max(diameters_um),
            0.7 * np.max(hist_counts),
            f"σ̂ = {sigma_hat:.1f}",
            fontsize=12,
        )

        ax.set_xlabel("Fiber diameters (μm)")
        ax.set_ylabel("Histogram of fiber diameters over the muscle")
        ax.legend()

        return ax
