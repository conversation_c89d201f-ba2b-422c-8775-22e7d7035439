"""
ContractionProfile class for generating muscle contraction profiles.
Translated from MATLAB ContractionProfile class.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
from typing import Literal, Optional

# Use non-interactive backend for server environments
matplotlib.use("Agg")


class ContractionProfile:
    """
    Generate muscle contraction profiles for EMG simulation.

    This class creates time-varying contraction profiles with different shapes:
    - 'trapezoidal': Ramp up, plateau, ramp down
    - 'constant': Constant level throughout
    - 'ramp': Linear increase
    - 'rect': Rectangular pulse
    """

    def __init__(
        self,
        profile_len: float,
        fs: float,
        profile_type: Literal["trapezoidal", "constant", "ramp", "rect"],
        percent_mvc: float,
        slope: Optional[float] = None,
        silence: Optional[float] = None,
    ):
        """
        Initialize the contraction profile.

        Parameters
        ----------
        profile_len : float
            Total profile length in seconds
        fs : float
            Sampling frequency in Hz
        profile_type : Literal['trapezoidal', 'constant', 'ramp', 'rect']
            Type of profile to generate
        percent_mvc : float
            Peak contraction level as fraction of MVC (0-1)
        slope : Optional[float]
            Slope for trapezoidal/ramp profiles (fraction per second)
        silence : Optional[float]
            Silent period at beginning and end (seconds)
        """
        self.profile_len = profile_len
        self.fs = fs
        self.profile_type = profile_type
        self.percent_mvc = percent_mvc
        self.slope = slope if slope is not None else 0.1  # Default 10%/second
        self.silence = silence if silence is not None else 1.0  # Default 1 second

        # Calculate total number of samples
        self.T = int(profile_len * fs)

        # Generate the profile
        self.profile = self._generate_profile()

        # Create timeline (matching MATLAB implementation)
        self.timeline = np.arange(len(self.profile)) / self.fs

        # Initialize subsampled profile as None (will be created when needed)
        self.sub_profile = None

    def _generate_profile(self) -> np.ndarray:
        """Generate the contraction profile based on type."""
        t = np.arange(self.T) / self.fs  # Time vector in seconds
        profile = np.zeros(self.T)

        if self.profile_type == "constant":
            # Constant profile with silence periods
            start_idx = int(self.silence * self.fs)
            end_idx = int((self.profile_len - self.silence) * self.fs)
            profile[start_idx:end_idx] = self.percent_mvc

        elif self.profile_type == "trapezoidal":
            # Trapezoidal profile: ramp up, plateau, ramp down
            start_silence = int(self.silence * self.fs)
            end_silence = int(self.silence * self.fs)

            # Calculate ramp duration based on slope
            ramp_duration = self.percent_mvc / self.slope  # seconds
            ramp_samples = int(ramp_duration * self.fs)

            # Available time for the profile (excluding silence)
            active_time = self.profile_len - 2 * self.silence
            active_samples = int(active_time * self.fs)

            if 2 * ramp_samples >= active_samples:
                # Not enough time for full ramps, make triangular
                ramp_samples = active_samples // 2
                plateau_samples = 0
            else:
                plateau_samples = active_samples - 2 * ramp_samples

            # Build the trapezoidal profile
            current_idx = start_silence

            # Ramp up
            if ramp_samples > 0:
                ramp_up = np.linspace(0, self.percent_mvc, ramp_samples)
                profile[current_idx : current_idx + ramp_samples] = ramp_up
                current_idx += ramp_samples

            # Plateau
            if plateau_samples > 0:
                profile[current_idx : current_idx + plateau_samples] = self.percent_mvc
                current_idx += plateau_samples

            # Ramp down
            if ramp_samples > 0:
                ramp_down = np.linspace(self.percent_mvc, 0, ramp_samples)
                profile[current_idx : current_idx + ramp_samples] = ramp_down

        elif self.profile_type == "ramp":
            # Linear ramp profile
            start_idx = int(self.silence * self.fs)
            end_idx = int((self.profile_len - self.silence) * self.fs)

            active_duration = (end_idx - start_idx) / self.fs
            ramp_values = np.linspace(0, self.percent_mvc, end_idx - start_idx)
            profile[start_idx:end_idx] = ramp_values

        elif self.profile_type == "rect":
            # Rectangular pulse profile
            start_idx = int(self.silence * self.fs)
            # Pulse duration is slope parameter (in seconds)
            pulse_duration = self.slope if self.slope is not None else 2.0
            end_idx = min(
                start_idx + int(pulse_duration * self.fs),
                int((self.profile_len - self.silence) * self.fs),
            )
            profile[start_idx:end_idx] = self.percent_mvc

        return profile

    def subsample(self, target_fs: float):
        """
        Create a subsampled version of the profile.

        Parameters
        ----------
        target_fs : float
            Target sampling frequency for subsampling
        """
        if target_fs >= self.fs:
            self.sub_profile = self.profile.copy()
            return

        # Calculate subsampling factor
        subsample_factor = int(self.fs / target_fs)

        # Subsample the profile
        self.sub_profile = self.profile[::subsample_factor]

    def show(self, save_path: Optional[str] = None):
        """
        Display the contraction profile.

        Parameters
        ----------
        save_path : Optional[str]
            If provided, save the plot to this path instead of displaying
        """
        plt.figure(figsize=(10, 6))

        # Time vector for plotting
        t = np.arange(self.T) / self.fs

        # Plot main profile
        plt.plot(t, self.profile * 100, "b-", linewidth=2, label="Profile")

        # Plot subsampled profile if it exists
        if self.sub_profile is not None:
            t_sub = (
                np.arange(len(self.sub_profile))
                * (self.fs / len(self.sub_profile) * len(t))
                / self.fs
            )
            plt.plot(
                t_sub,
                self.sub_profile * 100,
                "ro-",
                markersize=4,
                label="Subsampled",
                alpha=0.7,
            )

        plt.xlabel("Time (s)")
        plt.ylabel("Contraction Level (% MVC)")
        plt.title(f"{self.profile_type.capitalize()} Contraction Profile")
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.xlim(0, self.profile_len)
        plt.ylim(-5, max(105, self.percent_mvc * 110))

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")
            print(f"Profile plot saved to: {save_path}")
        else:
            plt.show()

        plt.close()
