import numpy as np
import matplotlib.pyplot as plt
from typing import Op<PERSON>, <PERSON>ple, List


class MuscleForceMdl:
    """
    This class encapsulates the:
    - twitch models for N MUs
    - faster-than-linear coefficients for force calculation
    - offline force calculation
    - online force calculation
    """

    def __init__(self, N: int, size_magnitude: float, hfs: float):
        """
        Initialize the muscle force model.

        Parameters:
        -----------
        N: int
            Number of motor neurons.
        size_magnitude: float
            Size magnitude parameter.
        hfs: float
            Modelling frequency (high).
        """
        # Twitches parameters
        self.Tmax = None  # Maximum time-to-peak delay (see Fuglevand)
        self.Tr = 3  # Time to peak range
        self.Tcoeff = None
        self.T = None  # Times to peak
        self.P = None  # Peak heights

        # Twitches
        self.twitch_mat = None
        self.twitch_cell = None

        # General
        self.hfs = hfs  # Modelling frequency (high)
        self.N = N  # Number of motor neurons
        self.size_magnitude = size_magnitude
        self.fmax = None  # Maximal voluntary contraction (for normalization)
        self.online_buffer = None

        # Quasistatic exc2force model
        self.qs_e2f_mdl = None
        # Quasistatic force2exc model
        self.qs_f2e_mdl = None

        # Initialize the model
        self.init_twitch_parameters()
        self.init_twitches()

    def init_twitch_parameters(self):
        """
        Initialize twitch parameters based on Fuglevand et al. 1993.

        Peak twitch forces: proportional to sizes
        Fuglevand's model: twitch(t) = Pf/Tf * t .* exp(1 - t/Tf);
        Tf is time to peak; Pf is peak height;
        """
        # Peak heights:
        self.P = np.exp(
            (np.log(self.size_magnitude) / self.N) * np.arange(1, self.N + 1)
        )

        # Times to peak:
        self.Tmax = (
            90 / 1000 * self.hfs
        )  # Maximum time-to-peak delay: 90 ms (Fuglevand)
        self.Tr = 3  # Time to peak range
        self.Tcoeff = np.log(self.size_magnitude) / np.log(self.Tr)
        self.T = self.Tmax * (1.0 / self.P) ** (
            1 / self.Tcoeff
        )  # see Eq. 15 of Fuglevand

    def init_twitches(self):
        """
        Precalculate the twitches and save them to a matrix (equalized lengths)
        and list (individual effective lengths).
        """
        max_twitch_len = int(np.ceil(5 * np.max(self.T)))
        twitch_timeline = np.arange(max_twitch_len)
        self.twitch_cell = []
        self.twitch_mat = np.zeros((max_twitch_len, self.N))

        for i in range(self.N):
            twitch = self.twitch_fuglevand(twitch_timeline, self.P[i], self.T[i])
            self.twitch_mat[:, i] = twitch

            effective_length = min(len(twitch), int(np.ceil(5 * self.T[i])))
            self.twitch_cell.append(twitch[:effective_length])

    def twitch_fuglevand(self, t: np.ndarray, P: float, T: float) -> np.ndarray:
        """
        Calculate Fuglevand twitch model.
        This function corresponds to Fuglevand's twitch model, see Fuglevand et al. 1993.

        Parameters:
        -----------
        t: np.ndarray
            Time vector.
        P: float
            Peak height.
        T: float
            Time to peak.

        Returns:
        --------
        np.ndarray
            Twitch response.
        """
        return P / T * t * np.exp(1 - t / T)

    def get_gain(self, ipi: np.ndarray, T: float) -> np.ndarray:
        """
        Returns the gain value for the force output for a motor unit with current
        inter-pulse-interval ipi and T-parameter of the twitch T. This function
        corresponds to Fuglevand's nonlinear gain model for the force output,
        see Fuglevand - Models of Rate Coding ..., eq. 17.

        Parameters:
        -----------
        ipi: np.ndarray
            Inter-pulse interval vector.
        T: float
            T-parameter of the twitch.

        Returns:
        --------
        np.ndarray
            Gain vector.
        """
        Sf = lambda x: 1 - np.exp(-2 * x**3)

        inst_dr = T / ipi  # Instantaneous discharge rate
        gain = np.ones_like(inst_dr)  # Gain

        mask = inst_dr > 0.4
        gain[mask] = (Sf(inst_dr[mask]) / inst_dr[mask]) / (Sf(0.4) / 0.4)

        return gain

    def plot_twitches(self, ax: Optional[plt.Axes] = None) -> plt.Figure:
        """Plot twitch waveforms."""
        if ax is None:
            fig, ax = plt.subplots()
        else:
            fig = ax.get_figure()

        timeline = np.arange(self.twitch_mat.shape[0]) / self.hfs
        ax.plot(timeline, self.twitch_mat, "k")
        ax.set_title("Twitch waveforms")
        ax.set_xlabel("Time, s")
        ax.set_ylabel("Amplitude")

        return fig

    def generate_force_offline(
        self, spikes: np.ndarray, prefix: str = ""
    ) -> np.ndarray:
        """Generate force offline from spike trains."""
        L = spikes.shape[0]

        # IPI signal generation out of spikes signal (for gain nonlinearity)
        # Note: You'll need to implement sawtooth2ipi and spikes2sawtooth functions
        extended_spikes = np.vstack([spikes[1:, :], np.zeros((1, self.N))])
        sawtooth = self.spikes2sawtooth(extended_spikes)
        _, ipi = self.sawtooth2ipi(sawtooth)

        gain = np.full_like(spikes, np.nan)
        for n in range(self.N):
            gain[:, n] = self.get_gain(ipi[:, n], self.T[n])

        # Generate force
        force_hfs = np.zeros(L)
        for n in range(self.N):
            for t in range(L):
                if spikes[t, n]:
                    twitch_to_add = self.twitch_cell[n]
                    to_take = min(len(twitch_to_add), L - t)
                    force_hfs[t : t + to_take] += gain[t, n] * twitch_to_add[:to_take]
            print(f"{prefix} {n + 1} Twitch trains are generated")

        force_hfs = force_hfs / self.fmax  # Normalization to % MVC
        return force_hfs

    def init_online_buffer(self, length: Optional[int] = None):
        """Initialize online buffer for real-time force calculation."""
        if length is None:
            length = self.twitch_mat.shape[0]
        self.online_buffer = np.zeros(length)

    def generate_force_online(self, spikes: np.ndarray, ipi: np.ndarray) -> float:
        """
        Generate force online (real-time).

        Args:
            spikes: Spike vector for current time step (shape: (N,))
            ipi: Inter-pulse interval vector for current time step (shape: (N,))

        Returns:
            Current force value
        """
        if spikes.ndim != 1 or ipi.ndim != 1:
            raise ValueError(
                "To work in online manner, pass the spikes vector element by element"
            )

        spikes = spikes.flatten()
        ipi = ipi.flatten()

        for i in range(self.N):
            if spikes[i]:
                to_add = self.get_gain(ipi[i], self.T[i]) * self.twitch_mat[:, i]
                self.online_buffer += to_add

        # Get the current contraction
        force = self.online_buffer[0] / self.fmax

        # Shift buffer
        self.online_buffer = np.concatenate([self.online_buffer[1:], [0]])

        return force

    def normalize_mvc(self, spikes: np.ndarray) -> Tuple[np.ndarray, float]:
        """Normalize to maximum voluntary contraction (MVC)."""
        self.fmax = 1
        try:
            mvc_force = self.generate_force_offline(spikes, "MVC measurement:")
            fmax = np.mean(mvc_force[round(len(mvc_force) / 2) :])
        except Exception as err:
            self.fmax = None
            raise err

        self.fmax = fmax
        mvc_force = mvc_force / self.fmax
        return mvc_force, fmax

    def init_quasistatic_e2f_f2e_models(self, mu_pool):
        """Initialize quasistatic excitation-to-force and force-to-excitation models."""
        qsi_T = 25 * self.hfs
        qsi_excitation = np.linspace(0, 1, int(qsi_T))

        # Unpack the tuple returned by generate_spike_train_gauss
        qsi_spikes, _, _ = mu_pool.mn_pool.generate_spike_train_gauss(
            np.arange(1, int(qsi_T) + 1),
            np.full(mu_pool.mn_pool.N, np.nan),
            qsi_excitation,
            self.hfs,
        )
        qsi_force = self.generate_force_offline(qsi_spikes)

        # Invert the curve, get weighted polynomial interpolation that passes through zero
        w = 1.25 - qsi_excitation

        # Force to excitation model
        A_f2e = w[:, np.newaxis] * np.column_stack(
            [qsi_force**5, qsi_force**4, qsi_force**3, qsi_force**2, qsi_force**1]
        )
        b_f2e = w * qsi_excitation
        self.qs_f2e_mdl = np.concatenate(
            [np.linalg.lstsq(A_f2e, b_f2e, rcond=None)[0], [0]]
        )

        # Excitation to force model
        A_e2f = w[:, np.newaxis] * np.column_stack(
            [
                qsi_excitation**5,
                qsi_excitation**4,
                qsi_excitation**3,
                qsi_excitation**2,
                qsi_excitation**1,
            ]
        )
        b_e2f = w * qsi_force
        self.qs_e2f_mdl = np.concatenate(
            [np.linalg.lstsq(A_e2f, b_e2f, rcond=None)[0], [0]]
        )

    def e2f(self, e: np.ndarray) -> np.ndarray:
        """Convert excitation to force using quasistatic model."""
        return np.polyval(self.qs_e2f_mdl, e)

    def f2e(self, f: np.ndarray) -> np.ndarray:
        """Convert force to excitation using quasistatic model."""
        return np.polyval(self.qs_f2e_mdl, f)

    def spikes2sawtooth(
        self, spikes: np.ndarray, initial_values: Optional[np.ndarray] = None
    ) -> np.ndarray:
        """
        Convert spikes to sawtooth signal.

        Parameters:
        -----------
        spikes: np.ndarray
            Spike train matrix (time x neurons).
        initial_values: np.ndarray, optional
            Initial values for each neuron. Default is ones.

        Returns:
        --------
        np.ndarray
            Sawtooth sequence.
        """
        if initial_values is None:
            initial_values = np.ones(spikes.shape[1])

        l, w = spikes.shape
        seq = np.zeros((l, w))

        # Set initial values, but reset to 0 if there's a spike at t=0
        initial_values = initial_values * (spikes[0] != 1)
        seq[0] = initial_values

        for i in range(1, l):
            spike_mask = spikes[i].astype(bool)
            seq[i] = np.where(spike_mask, 0, seq[i - 1] + 1)

        return seq

    def sawtooth2spikes(self, sawtooth: np.ndarray) -> np.ndarray:
        """
        Convert sawtooth signal to spikes.
        A spike occurs when the sawtooth resets to 0.

        Parameters:
        -----------
        sawtooth: np.ndarray
            Sawtooth signal matrix.

        Returns:
        --------
        np.ndarray
            Spike train matrix.
        """
        spikes = np.zeros_like(sawtooth, dtype=bool)

        # First sample: spike if sawtooth starts at 0
        spikes[0, :] = sawtooth[0, :] == 0

        # Subsequent samples: spike when sawtooth decreases (resets)
        for i in range(1, sawtooth.shape[0]):
            spikes[i, :] = sawtooth[i, :] < sawtooth[i - 1, :]

        return spikes.astype(int)

    def sawtooth2ipi(
        self, sawtooth: np.ndarray, ipi_saturation: float = np.inf
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        Convert sawtooth signal to inter-pulse intervals.

        Parameters:
        -----------
        sawtooth: np.ndarray
            Sawtooth signal matrix.
        ipi_saturation: float, optional
            Maximum IPI value (saturation). Default is infinity.

        Returns:
        --------
        Tuple[np.ndarray, np.ndarray]
            ipi_filled: IPI signal.
            ipi_filled_seamless: IPI signal with zeros filled by preceding values.
        """
        # Convert sawtooth to spikes
        spikes = self.sawtooth2spikes(sawtooth)

        # Reverse spikes
        spikes_reversed = np.flipud(spikes)

        # Convert reversed spikes back to sawtooth with initial value from original sawtooth
        initial_val = (
            sawtooth[0, :] if sawtooth.size > 0 else np.zeros(sawtooth.shape[1])
        )
        i_sawtooth = self.spikes2sawtooth(spikes_reversed, initial_val)

        # Reverse back
        i_sawtooth = np.flipud(i_sawtooth)

        # Add to original sawtooth
        ipi_filled = sawtooth + i_sawtooth
        ipi_filled = np.minimum(ipi_filled, ipi_saturation)

        # Create seamless version
        ipi_filled_seamless = ipi_filled.copy()

        # Find zeros and replace with preceding values
        zero_mask = ipi_filled == 0
        for j in range(ipi_filled.shape[1]):
            zero_indices = np.where(zero_mask[:, j])[0]
            for idx in zero_indices:
                if idx > 0:
                    ipi_filled_seamless[idx, j] = ipi_filled_seamless[idx - 1, j]

        ipi_filled_seamless = np.minimum(ipi_filled_seamless, ipi_saturation)

        return ipi_filled, ipi_filled_seamless
