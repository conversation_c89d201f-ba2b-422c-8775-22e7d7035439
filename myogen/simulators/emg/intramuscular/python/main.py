"""
Main script to run the EMG intramuscular simulation pipeline.
Executes the scripts in sequence to maintain shared namespace.
"""

import os
import sys

# Get the directory of this file
current_dir = os.path.dirname(os.path.abspath(__file__))

# Add the package root to the Python path if needed
package_root = os.path.join(current_dir, "..", "..", "..", "..")
if package_root not in sys.path:
    sys.path.insert(0, package_root)

print("Starting EMG intramuscular simulation pipeline...")
print("=" * 60)

# Execute scripts in sequence to maintain shared namespace
scripts = [
    "s01_cl_init_mn_pool.py",
    "s02_cl_init_mu_pool.py",
    "s03_cl_init_force_model.py",
    "s04_cl_tune_pid.py",
    "s05_cl_init_profile.py",
    "s06_cl_generate_force.py",
    "s07_cl_init_electrode.py",
    "s08_cl_init_muaps.py",
    "s09_cl_generate_mvc_emg.py",
    "s10_cl_generate_emg.py",
    "s11_cl_get_detectable_mus.py",
    "s12_cl_generate_annotation.py",
    "s13_cl_generate_dictionary.py",
    "s14_cl_generate_annotation_for_decomp.py",
    "s15_cl_reconstruct_signal.py",
]

# Create a shared global namespace for all scripts
shared_globals = {}

for script in scripts:
    script_path = os.path.join(current_dir, "sections", script)
    print(f"\nExecuting {script}...")
    print("-" * 40)

    try:
        with open(script_path, "r") as f:
            script_content = f.read()

        # Execute the script in the shared namespace
        exec(script_content, shared_globals)

        print(f"✓ {script} completed successfully")

    except Exception as e:
        print(f"✗ Error in {script}: {e}")
        raise

print("\n" + "=" * 60)
print("EMG intramuscular simulation pipeline completed successfully!")
print("All models are initialized and ready for use.")
