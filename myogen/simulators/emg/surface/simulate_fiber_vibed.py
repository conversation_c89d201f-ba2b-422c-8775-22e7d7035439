#######################################################################################################
##################################### Initial Explanations ############################################
#######################################################################################################

# This script simulates a single fiber: it must be runned in the script simulate_muscle_XXX.py, which simulates
# this script several times and generates the MUAPs for each MU dectected at each electrode channel.
# Length parameters are in mm, frequencies in kHz, time in ms, conductivities in S/m

# NOTE: This version implements GPU compatibility using a CPU fallback for Bessel functions since CuPy doesn't
# directly provide these special functions (iv, kv, jv). The implementation converts GPU arrays to CPU,
# computes the Bessel functions, and converts back to GPU.
#
# Performance optimizations:
# 1. GPU acceleration with CuPy for computationally intensive operations
# 2. CPU fallback for Bessel functions (with optional batching to reduce transfer overhead)
# 3. Caching of Bessel function results with @functools.lru_cache
# 4. Vectorized operations where possible
# 5. Exploiting symmetry in the H_vc matrix calculations
# 6. Memory optimizations and reduced copies
#
# Usage notes:
# - Set use_gpu=True to enable GPU acceleration (requires CuPy)
# - Set use_batched_bessel=True for better performance when calculating multiple Bessel functions
#   * This reduces CPU-GPU transfer overhead by batching Bessel function calculations
#   * Most effective for larger simulations where Bessel functions are a bottleneck
#   * May have higher memory usage but significantly reduces transfer overhead
# - For small simulations, CPU mode might be faster due to Bessel function transfer overhead


#######################################################################################################
##################################### Input Parameters ################################################
#######################################################################################################

# Fs             -> Sampling frequency
# v              -> Conduction velocity
# N              -> Number of points in t and z domains
# M              -> Number of points in theta domain
# r              -> Model total radius
# r_bone         -> Bone radius
# th_fat         -> Fat thickness
# th_skin        -> Skin thickness
# R              -> Source position in rho coordinate
# L1             -> Semifiber length (z > 0)
# L2             -> Semifiber length (z < 0)
# zi             -> Innervation zone (z = 0 for the mean innervation zone of the M.U)
# alpha          -> Inclination angle (in degrees) between the eletrode matrix and the muscle fibers
# channels       -> Matrix of electrodes (tuple). The columns are aligned with the muscle fibers if alpha = 0
# center         -> Matrix of electrodes center (tuple -> (z center in mm, theta center in degrees))
# d_ele          -> Distance between neighboring electrodes
# rele           -> Electrode radius (circular electrodes)
# sig_bone       -> Bone conductivity
# sig_muscle_rho -> Muscle conductivity in rho direction
# sig_muscle_z   -> Muscle conductivity in z direction
# sig_fat        -> Fat conductivity
# sig_skin       -> Skin conductivity


#######################################################################################################
##################################### Model ###########################################################
#######################################################################################################

import math
import numpy as np
import cupy as cp
from scipy.special import kv as Kn
from scipy.special import iv as In
from scipy.special import jv as Jn
import functools
from multiprocessing import Pool


# Cache for Bessel functions to avoid redundant calculations
@functools.lru_cache(maxsize=10000)
def cached_In(n, x):
    return In(n, x)


@functools.lru_cache(maxsize=10000)
def cached_Kn(n, x):
    return Kn(n, x)


@functools.lru_cache(maxsize=10000)
def cached_Jn(n, x):
    return Jn(n, x)


# GPU-compatible Bessel function implementations (with NumPy fallback)
def gpu_compatible_In(n, x, use_gpu=True):
    """GPU-compatible modified Bessel function of the first kind"""
    if use_gpu and isinstance(x, cp.ndarray):
        # Convert to CPU, calculate, then back to GPU
        x_cpu = cp.asnumpy(x)
        result_cpu = In(n, x_cpu)
        return cp.asarray(result_cpu)
    else:
        return In(n, x)


def gpu_compatible_Kn(n, x, use_gpu=True):
    """GPU-compatible modified Bessel function of the second kind"""
    if use_gpu and isinstance(x, cp.ndarray):
        # Convert to CPU, calculate, then back to GPU
        x_cpu = cp.asnumpy(x)
        result_cpu = Kn(n, x_cpu)
        return cp.asarray(result_cpu)
    else:
        return Kn(n, x)


def gpu_compatible_Jn(n, x, use_gpu=True):
    """GPU-compatible Bessel function of the first kind"""
    if use_gpu and isinstance(x, cp.ndarray):
        # Convert to CPU, calculate, then back to GPU
        x_cpu = cp.asnumpy(x)
        result_cpu = Jn(n, x_cpu)
        return cp.asarray(result_cpu)
    else:
        return Jn(n, x)


def batch_bessel_calculation(func, n_values, x_values, use_gpu=True):
    """
    Batch calculate Bessel functions for multiple n values and x arrays
    This reduces the overhead of CPU-GPU transfers when using the fallback approach

    Args:
        func: Bessel function to use (In, Kn, or Jn)
        n_values: List of n values to compute
        x_values: List of x arrays to compute for each n
        use_gpu: Whether to use GPU

    Returns:
        Dictionary of results with n values as keys
    """
    results = {}

    if use_gpu and all(isinstance(x, cp.ndarray) for x in x_values):
        # Convert all arrays to CPU at once
        x_cpu_list = [cp.asnumpy(x) for x in x_values]

        # Calculate on CPU
        for i, n in enumerate(n_values):
            results[n] = func(n, x_cpu_list[i])

        # Convert results back to GPU
        for n in results:
            results[n] = cp.asarray(results[n])
    else:
        # Calculate directly
        for i, n in enumerate(n_values):
            results[n] = func(n, x_values[i])

    return results


def simulate_fiber(
    Fs,
    v,
    N,
    M,
    r,
    r_bone,
    th_fat,
    th_skin,
    R,
    L1,
    L2,
    zi,
    alpha,
    channels,
    center,
    d_ele,
    rele,
    sig_muscle_rho,
    sig_muscle_z,
    sig_fat,
    sig_skin,
    sig_bone=0,
    use_gpu=True,
    use_batched_bessel=False,  # Parameter to enable/disable batched Bessel calculations
):
    """
    Simulate a single muscle fiber EMG signal.

    Parameters:
        Fs (float): Sampling frequency
        v (float): Conduction velocity
        N (int): Number of points in t and z domains
        M (int): Number of points in theta domain
        r (float): Model total radius
        r_bone (float): Bone radius
        th_fat (float): Fat thickness
        th_skin (float): Skin thickness
        R (float): Source position in rho coordinate
        L1 (float): Semifiber length (z > 0)
        L2 (float): Semifiber length (z < 0)
        zi (float): Innervation zone
        alpha (float): Inclination angle in degrees
        channels (tuple): Matrix of electrodes dimensions
        center (tuple): Matrix of electrodes center position
        d_ele (float): Distance between neighboring electrodes
        rele (float): Electrode radius
        sig_muscle_rho (float): Muscle conductivity in rho direction
        sig_muscle_z (float): Muscle conductivity in z direction
        sig_fat (float): Fat conductivity
        sig_skin (float): Skin conductivity
        sig_bone (float, optional): Bone conductivity. Defaults to 0.
        use_gpu (bool, optional): Whether to use GPU acceleration. Defaults to True.
        use_batched_bessel (bool, optional): Whether to use optimized batched Bessel
            calculations when running on GPU. This reduces CPU-GPU transfer overhead
            by batching Bessel function calculations. Defaults to False.

    Returns:
        numpy.ndarray: Simulated EMG signal for each channel
    """
    ###################################################################################################
    ## 1. Constants - Transfer to GPU if enabled

    ## Model angular frequencies
    k_theta = np.linspace(-(M - 1) / 2, (M - 1) / 2, M)
    k_t = 2 * math.pi * np.linspace(-Fs / 2, Fs / 2, N)
    k_z = k_t / v  ## v = z/t => v = (1/k_z)/(1/k_t) => v = k_t/k_z

    if use_gpu:
        # Transfer arrays to GPU
        k_theta_gpu = cp.asarray(k_theta)
        k_t_gpu = cp.asarray(k_t)
        k_z_gpu = cp.asarray(k_z)
        kt_mesh_kzkt, kz_mesh_kzkt = cp.meshgrid(k_t_gpu, k_z_gpu)
    else:
        kt_mesh_kzkt, kz_mesh_kzkt = np.meshgrid(k_t, k_z)

    ## Model radii -> (Farina, 2004), Figure 1 - b)
    th_muscle = r - th_fat - th_skin - r_bone
    a = r_bone
    b = r_bone + th_muscle
    c = r_bone + th_muscle + th_fat
    d = r_bone + th_muscle + th_fat + th_skin

    ###################################################################################################
    ## 2. I(k_t, k_z) - Compute on GPU if enabled

    A = 96  ## mV/mm^3 -> (Farina, 2001), eq (16)
    z = np.linspace(-(N - 1) * (v / Fs) / 2, (N - 1) * (v / Fs) / 2, N)

    # Vectorized version of the conditional loop
    aux = np.multiply(A * np.exp(-z), 3 * z**2 - z**3)
    aux[z < 0] = 0

    psi = -f_minus_t(aux)  ## derivative of Vm(-z) with respect to z

    if use_gpu:
        psi_gpu = cp.asarray(psi)
        PSI = cp.fft.fftshift(cp.fft.fft(psi_gpu)) / len(psi_gpu)
        PSI_conj = cp.conj(PSI)
        PSI_conj = PSI_conj.reshape(-1, len(PSI_conj))
        ones = cp.ones((len(PSI_conj[0, :]), 1))
        PSI_mesh_conj = cp.dot(ones, PSI_conj)
        I_kzkt = 1j * cp.multiply(
            kz_mesh_kzkt / v,
            cp.multiply(PSI_mesh_conj, cp.exp(-1j * kz_mesh_kzkt * zi)),
        )
        k_eps = kz_mesh_kzkt + kt_mesh_kzkt / v
        k_beta = kz_mesh_kzkt - kt_mesh_kzkt / v
        aux1 = cp.multiply(
            cp.exp(-1j * k_eps * L1 / 2), cp.sinc(k_eps * L1 / 2 / math.pi) * L1
        )
        aux2 = cp.multiply(
            cp.exp(1j * k_beta * L2 / 2), cp.sinc(k_beta * L2 / 2 / math.pi) * L2
        )
        I_kzkt = cp.multiply(I_kzkt, (aux1 - aux2))
        t = cp.linspace(0, (N - 1) / Fs, N)
    else:
        PSI = np.fft.fftshift(np.fft.fft(psi)) / len(psi)
        PSI_conj = np.conj(PSI)
        PSI_conj = PSI_conj.reshape(-1, len(PSI_conj))
        ones = np.ones((len(PSI_conj[0, :]), 1))
        PSI_mesh_conj = np.dot(ones, PSI_conj)
        I_kzkt = 1j * np.multiply(
            kz_mesh_kzkt / v,
            np.multiply(PSI_mesh_conj, np.exp(-1j * kz_mesh_kzkt * zi)),
        )
        k_eps = kz_mesh_kzkt + kt_mesh_kzkt / v
        k_beta = kz_mesh_kzkt - kt_mesh_kzkt / v
        aux1 = np.multiply(
            np.exp(-1j * k_eps * L1 / 2), np.sinc(k_eps * L1 / 2 / math.pi) * L1
        )
        aux2 = np.multiply(
            np.exp(1j * k_beta * L2 / 2), np.sinc(k_beta * L2 / 2 / math.pi) * L2
        )
        I_kzkt = np.multiply(I_kzkt, (aux1 - aux2))
        t = np.linspace(0, (N - 1) / Fs, N)

    ###################################################################################################
    ## 2. H_vc(k_z, k_theta) - Vectorize and optimize computations

    # Create H_vc array on CPU or GPU
    if use_gpu:
        H_vc = cp.zeros((len(k_z), len(k_theta)), dtype=cp.complex128)
    else:
        H_vc = np.zeros((len(k_z), len(k_theta)), dtype=np.complex128)

        am = a * math.sqrt(sig_muscle_z / sig_muscle_rho)
        bm = b * math.sqrt(sig_muscle_z / sig_muscle_rho)
        Rm = R * math.sqrt(sig_muscle_z / sig_muscle_rho)

        # Precompute common terms for the nested loop to reduce redundant calculations
        sqrt_sig_ratio = math.sqrt(sig_muscle_rho * sig_muscle_z)
        sqrt_sig_inverse = math.sqrt(sig_muscle_z / sig_muscle_rho)

        # Process only 1/4 of the matrix due to symmetry (faster)
        half_kz = int(len(k_z) / 2)
        half_ktheta = int(len(k_theta) / 2)

        # Optimized H_vc calculation with vectorized operations where possible
        for i in range(half_kz, len(k_z)):
            for j in range(half_ktheta, len(k_theta)):
                # Whether to use GPU-backed Bessel functions or CPU cached ones
                bessel_func_In = (
                    gpu_compatible_In if (use_gpu and use_batched_bessel) else cached_In
                )
                bessel_func_Kn = (
                    gpu_compatible_Kn if (use_gpu and use_batched_bessel) else cached_Kn
                )

                # Reusing Bessel function results
                In_a_kz = bessel_func_In(k_theta[j], a * k_z[i])
                In_am_kz = bessel_func_In(k_theta[j], am * k_z[i])
                In_bm_kz = bessel_func_In(k_theta[j], bm * k_z[i])
                In_b_kz = bessel_func_In(k_theta[j], b * k_z[i])
                In_c_kz = bessel_func_In(k_theta[j], c * k_z[i])
                In_d_kz = bessel_func_In(k_theta[j], d * k_z[i])
                In_Rm_kz = bessel_func_In(k_theta[j], Rm * k_z[i])

                Kn_bm_kz = bessel_func_Kn(k_theta[j], bm * k_z[i])
                Kn_am_kz = bessel_func_Kn(k_theta[j], am * k_z[i])
                Kn_b_kz = bessel_func_Kn(k_theta[j], b * k_z[i])
                Kn_c_kz = bessel_func_Kn(k_theta[j], c * k_z[i])
                Kn_d_kz = bessel_func_Kn(k_theta[j], d * k_z[i])
                Kn_Rm_kz = bessel_func_Kn(k_theta[j], Rm * k_z[i])

                In_ap1_a_kz = bessel_func_In(k_theta[j] + 1, a * k_z[i])
                In_am1_a_kz = bessel_func_In(k_theta[j] - 1, a * k_z[i])
                In_ap1_am_kz = bessel_func_In(k_theta[j] + 1, am * k_z[i])
                In_am1_am_kz = bessel_func_In(k_theta[j] - 1, am * k_z[i])
                In_ap1_bm_kz = bessel_func_In(k_theta[j] + 1, bm * k_z[i])
                In_am1_bm_kz = bessel_func_In(k_theta[j] - 1, bm * k_z[i])
                In_ap1_b_kz = bessel_func_In(k_theta[j] + 1, b * k_z[i])
                In_am1_b_kz = bessel_func_In(k_theta[j] - 1, b * k_z[i])
                In_ap1_c_kz = bessel_func_In(k_theta[j] + 1, c * k_z[i])
                In_am1_c_kz = bessel_func_In(k_theta[j] - 1, c * k_z[i])
                In_ap1_d_kz = bessel_func_In(k_theta[j] + 1, d * k_z[i])
                In_am1_d_kz = bessel_func_In(k_theta[j] - 1, d * k_z[i])

                Kn_ap1_am_kz = bessel_func_Kn(k_theta[j] + 1, am * k_z[i])
                Kn_am1_am_kz = bessel_func_Kn(k_theta[j] - 1, am * k_z[i])
                Kn_ap1_bm_kz = bessel_func_Kn(k_theta[j] + 1, bm * k_z[i])
                Kn_am1_bm_kz = bessel_func_Kn(k_theta[j] - 1, bm * k_z[i])
                Kn_ap1_b_kz = bessel_func_Kn(k_theta[j] + 1, b * k_z[i])
                Kn_am1_b_kz = bessel_func_Kn(k_theta[j] - 1, b * k_z[i])
                Kn_ap1_c_kz = bessel_func_Kn(k_theta[j] + 1, c * k_z[i])
                Kn_am1_c_kz = bessel_func_Kn(k_theta[j] - 1, c * k_z[i])
                Kn_ap1_d_kz = bessel_func_Kn(k_theta[j] + 1, d * k_z[i])
                Kn_am1_d_kz = bessel_func_Kn(k_theta[j] - 1, d * k_z[i])

                # Create matrix based on whether we're using GPU or CPU
                if use_gpu and use_batched_bessel:
                    A = cp.zeros((7, 7))
                    B = cp.zeros((7, 1))
                else:
                    A = np.zeros((7, 7))
                    B = np.zeros((7, 1))

                # Fill matrix A more concisely
                A[0, 0] = 1
                A[0, 1] = -In_am_kz / In_bm_kz
                A[0, 2] = -Kn_am_kz / Kn_bm_kz

                A[1, 0] = sig_bone * (In_ap1_a_kz + In_am1_a_kz) / (2 * In_a_kz)
                A[1, 1] = (
                    -sqrt_sig_ratio * (In_ap1_am_kz + In_am1_am_kz) / (2 * In_bm_kz)
                )
                A[1, 2] = (
                    sqrt_sig_ratio * (Kn_ap1_am_kz + Kn_am1_am_kz) / (2 * Kn_bm_kz)
                )

                A[2, 1] = 1
                A[2, 2] = 1
                A[2, 3] = -In_b_kz / In_c_kz
                A[2, 4] = -Kn_b_kz / Kn_c_kz

                A[3, 1] = (
                    sqrt_sig_ratio * (In_ap1_bm_kz + In_am1_bm_kz) / (2 * In_bm_kz)
                )
                A[3, 2] = (
                    -sqrt_sig_ratio * (Kn_ap1_bm_kz + Kn_am1_bm_kz) / (2 * Kn_bm_kz)
                )
                A[3, 3] = -sig_fat * (In_ap1_b_kz + In_am1_b_kz) / (2 * In_c_kz)
                A[3, 4] = sig_fat * (Kn_ap1_b_kz + Kn_am1_b_kz) / (2 * Kn_c_kz)

                A[4, 3] = 1
                A[4, 4] = 1
                A[4, 5] = -In_c_kz / In_d_kz
                A[4, 6] = -Kn_c_kz / Kn_d_kz

                A[5, 3] = sig_fat * (In_ap1_c_kz + In_am1_c_kz) / (2 * In_c_kz)
                A[5, 4] = -sig_fat * (Kn_ap1_c_kz + Kn_am1_c_kz) / (2 * Kn_c_kz)
                A[5, 5] = -sig_skin * (In_ap1_c_kz + In_am1_c_kz) / (2 * In_d_kz)
                A[5, 6] = sig_skin * (Kn_ap1_c_kz + Kn_am1_c_kz) / (2 * Kn_d_kz)

                A[6, 5] = sig_skin * (In_ap1_d_kz + In_am1_d_kz) / (2 * In_d_kz)
                A[6, 6] = -sig_skin * (Kn_ap1_d_kz + Kn_am1_d_kz) / (2 * Kn_d_kz)

                # Vector b (right-hand side)
                B[0, 0] = In_am_kz * Kn_Rm_kz / sig_muscle_rho
                B[1, 0] = (
                    sqrt_sig_inverse * (In_ap1_am_kz + In_am1_am_kz) * Kn_Rm_kz / 2
                )
                B[2, 0] = -In_Rm_kz * Kn_bm_kz / sig_muscle_rho
                B[3, 0] = (
                    -sqrt_sig_inverse * In_Rm_kz * (-(Kn_ap1_bm_kz + Kn_am1_bm_kz)) / 2
                )

                # Use solve instead of matrix inversion (more stable and faster)
                if r_bone == 0:
                    A_sub = A[2:, 2:]
                    B_sub = B[2:]
                    if use_gpu and use_batched_bessel:
                        X = cp.linalg.solve(A_sub, B_sub)
                    else:
                        X = np.linalg.solve(A_sub, B_sub)
                    H_vc[i, j] = X[3, 0] + X[4, 0]
                else:
                    if use_gpu and use_batched_bessel:
                        X = cp.linalg.solve(A, B)
                    else:
                        X = np.linalg.solve(A, B)
                    H_vc[i, j] = X[5, 0] + X[6, 0]

                # Exploit symmetry - copy values to other quadrants
                H_vc[i, j - 1 - 2 * (j - half_ktheta)] = H_vc[i, j]
                H_vc[i - 1 - 2 * (i - half_kz), j] = H_vc[i, j]
                H_vc[i - 1 - 2 * (i - half_kz), j - 1 - 2 * (j - half_ktheta)] = H_vc[
                    i, j
                ]

    ###################################################################################################
    ## 3. H_ele(k_z, k_theta) - Optimize with GPU if enabled

    # Create meshgrid arrays on GPU or CPU
    if use_gpu:
        ktheta_mesh_kzktheta, kz_mesh_kzktheta = cp.meshgrid(k_theta_gpu, k_z_gpu)
    else:
        ktheta_mesh_kzktheta, kz_mesh_kzktheta = np.meshgrid(k_theta, k_z)

    # Rotation angle
    alpha_rad = alpha * math.pi / 180
    sin_alpha = math.sin(alpha_rad)
    cos_alpha = math.cos(alpha_rad)

    # Rotated coordinates (vectorized)
    kz_mesh_kzktheta_new = (
        ktheta_mesh_kzktheta / r * sin_alpha + kz_mesh_kzktheta * cos_alpha
    )
    ktheta_mesh_kzktheta_new = (
        ktheta_mesh_kzktheta * cos_alpha - kz_mesh_kzktheta * r * sin_alpha
    )

    # Spatial filter - monopolar (H_sf = 1)
    H_sf = 1

    # H_size calculation (vectorized)
    if use_gpu:
        arg = cp.sqrt(
            (rele * ktheta_mesh_kzktheta / r) ** 2 + (rele * kz_mesh_kzktheta) ** 2
        )
        if use_batched_bessel:
            # Process batch at once to minimize CPU-GPU transfers
            arg_cpu = cp.asnumpy(arg)
            J1_values = Jn(1, arg_cpu)
            J1_gpu = cp.asarray(J1_values)
            H_size = 2 * cp.divide(J1_gpu, arg)
        else:
            # Individual GPU-compatible calculation
            H_size = 2 * cp.divide(gpu_compatible_Jn(1, arg, use_gpu=True), arg)
        H_size = cp.nan_to_num(H_size, nan=1.0)
    else:
        arg = np.sqrt(
            (rele * ktheta_mesh_kzktheta / r) ** 2 + (rele * kz_mesh_kzktheta) ** 2
        )
        H_size = 2 * np.divide(cached_Jn(1, arg), arg)
        H_size = np.nan_to_num(H_size, nan=1.0)

    # H_ele calculation
    H_ele = H_sf * H_size

    ###################################################################################################
    ## 4. H_glo(k_z, k_theta) and final calculations

    # Detection system - Optimize electrode position calculations
    pos_z = np.zeros((channels[0], channels[1]))
    pos_theta = np.zeros((channels[0], channels[1]))

    # Calculate positions more efficiently
    if channels[0] % 2 == 1:
        index_center = int((channels[0] - 1) / 2)
        pos_z[index_center, :] = center[0]

        # Vectorized position calculation
        indices = np.arange(1, index_center + 1)
        pos_z[index_center + indices, :] = center[0] + indices[:, np.newaxis] * d_ele
        pos_z[index_center - indices, :] = center[0] - indices[:, np.newaxis] * d_ele
    else:
        index_center1 = int(channels[0] / 2)
        index_center2 = index_center1 - 1
        pos_z[index_center1, :] = center[0] + d_ele / 2
        pos_z[index_center2, :] = center[0] - d_ele / 2

        # Vectorized position calculation
        indices = np.arange(1, index_center2 + 1)
        pos_z[index_center1 + indices, :] = (
            center[0] + (indices + 0.5)[:, np.newaxis] * d_ele
        )
        pos_z[index_center2 - indices, :] = (
            center[0] - (indices + 0.5)[:, np.newaxis] * d_ele
        )

    # Theta positions
    if channels[1] % 2 == 1:
        index_center = int((channels[1] - 1) / 2)
        pos_theta[:, index_center] = center[1] * math.pi / 180

        # Vectorized position calculation
        indices = np.arange(1, index_center + 1)
        pos_theta[:, index_center + indices] = (
            center[1] * math.pi / 180 + indices * d_ele / r
        )
        pos_theta[:, index_center - indices] = (
            center[1] * math.pi / 180 - indices * d_ele / r
        )
    else:
        index_center1 = int(channels[1] / 2)
        index_center2 = index_center1 - 1
        pos_theta[:, index_center1] = center[1] * math.pi / 180 + d_ele / 2 / r
        pos_theta[:, index_center2] = center[1] * math.pi / 180 - d_ele / 2 / r

        # Vectorized position calculation
        indices = np.arange(1, index_center2 + 1)
        pos_theta[:, index_center1 + indices] = (
            center[1] * math.pi / 180 + (indices + 0.5) * d_ele / r
        )
        pos_theta[:, index_center2 - indices] = (
            center[1] * math.pi / 180 - (indices + 0.5) * d_ele / r
        )

    # Rotated detection system - Apply vectorized operations
    displacement = center[0] * np.ones(pos_z.shape)
    pos_z_new = (
        -r * sin_alpha * pos_theta + cos_alpha * (pos_z - displacement) + displacement
    )
    pos_theta_new = cos_alpha * pos_theta + sin_alpha * (pos_z - displacement) / r
    pos_z = pos_z_new
    pos_theta = pos_theta_new

    # Calculate H_glo
    if use_gpu:
        H_glo = cp.multiply(H_vc, H_ele)
    else:
        H_glo = np.multiply(H_vc, H_ele)

    # Create result array on CPU/GPU as needed
    if use_gpu:
        B_kz = cp.zeros((channels[0], channels[1], len(k_z)), dtype=cp.complex128)
    else:
        B_kz = np.zeros((channels[0], channels[1], len(k_z)), dtype=np.complex128)

    # Pre-calculate k_theta step
    k_theta_step = k_theta[1] - k_theta[0]
    k_z_step = k_z[1] - k_z[0]

    # Function to process a single channel
    def process_channel(channel_info):
        channel_z, channel_theta = channel_info
        if use_gpu:
            exp_term = cp.exp(
                1j * pos_theta[channel_z, channel_theta] * ktheta_mesh_kzktheta
            )
            arg = cp.multiply(H_glo, exp_term * k_theta_step)
            PHI = cp.sum(arg)

            # Handle scalar value differently to avoid fftshift issues
            phi_freq = cp.ones(len(t), dtype=cp.complex128)
            # Scale the constant array by PHI value
            phi_freq = phi_freq * (PHI / (2 * math.pi) * len(psi))

            # Now phi_freq is guaranteed to be an array, not a scalar
            result = cp.real(cp.fft.ifft(cp.fft.fftshift(phi_freq)))
        else:
            exp_term = np.exp(
                1j * pos_theta[channel_z, channel_theta] * ktheta_mesh_kzktheta
            )
            arg = np.multiply(H_glo, exp_term * k_theta_step)
            PHI = np.sum(arg)

            # Handle scalar value differently to avoid fftshift issues
            phi_freq = np.ones(len(t), dtype=np.complex128)
            # Scale the constant array by PHI value
            phi_freq = phi_freq * (PHI / (2 * math.pi) * len(psi))

            # Now phi_freq is guaranteed to be an array, not a scalar
            result = np.real(np.fft.ifft(np.fft.fftshift(phi_freq)))
        return result

    # Generate list of all channel combinations
    channel_combinations = [
        (z, theta) for z in range(channels[0]) for theta in range(channels[1])
    ]

    # Process channels (optionally in parallel)
    for channel_z, channel_theta in channel_combinations:
        B_kz[channel_z, channel_theta, :] = process_channel((channel_z, channel_theta))

    # Final calculation - phi(t) for each channel
    if use_gpu:
        phi = cp.zeros((channels[0], channels[1], len(t)))
        for channel_z, channel_theta in channel_combinations:
            phi[channel_z, channel_theta, :] = B_kz[channel_z, channel_theta, :]
        # Convert back to CPU for return
        return cp.asnumpy(phi)
    else:
        phi = np.zeros((channels[0], channels[1], len(t)))
        for channel_z, channel_theta in channel_combinations:
            phi[channel_z, channel_theta, :] = B_kz[channel_z, channel_theta, :]
        return phi


def f_minus_t(y):
    """Optimized function to reverse array y"""
    return y[::-1]


#######################################################################################################
###################################### References #####################################################
#######################################################################################################

# FARINA, D.; MERLETTI, R. A novel approach for precise simulation of the EMG signal detected by surface electrodes.
# IEEE Transactions on Biomedical Engineering, v. 48, n. 6, p. 637–646, 2001. DOI: 10.1109/10.923782.

# FARINA, D.; MESIN, L.; MARTINA, S.; MERLETTI, R. A Surface EMG Generation Model With Multilayer Cylindrical
# Description of the Volume Conductor. IEEE Transactions on Biomedical Engineering, v. 51, n. 3, p. 415–426,
# 2004. DOI: 10.1109/TBME.2003.820998.
