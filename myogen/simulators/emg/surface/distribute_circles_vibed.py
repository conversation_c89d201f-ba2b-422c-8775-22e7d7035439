#######################################################################################################
##################################### Initial Explanations ############################################
#######################################################################################################

# This script distributes the motor units inside the muscle and the fibers inside each motor unit,
# which are treated as circles. This script support two types of distributions: with unequal (for motor
# units inside the muscle) and equal circle sizes (for fibers inside a motor unit). They are distributed
# according an uniform distribution in the angular position and according an gaussian distribution
# centered in the middle for radial position.


import math
import numpy as np


def distribute_circles(radius, mode, weights):
    """
    Distributes smaller circles within a larger circle.

    Args:
        mode (string):
        'unequal' if smaller circles don't have the same radius.
        'equal' if smaller circles have same radius.

        radius (tuple or float):
        (1) tuple: if mode is 'equal', radius should be a tuple, which 1st position
        should be radius of external circle (float) and the 2nd position
        should be the smaller circles radii.
        (2) float: if model is 'unequal', radius should be the radius of external
        circle (float).

        weights (tuple or float):
        (1) int: if mode is 'equal', weights are the number of circles to be
        distributed
        (2) array: if model is 'unequal', weights is an array with the weight of
        each circle area


    Returns (tuple or list):
        (1) array: if mode is 'equal', the function returns an array, which
        contains the positions of each circle (tuple).
        (2): if model is 'unequal', the function returns a tuple, which 1st position
        contains an array with the positions of each circle (2d array) and the 2nd
        position contains an array of the radii of each circle (float)
    """
    if mode == "unequal":  ## circles to be distributed don't have the same radius
        n_circles = len(weights)

        # Special case for single circle - place in center
        if n_circles == 1:
            r_min = math.sqrt(radius**2 / sum(weights))
            single_radius = r_min * math.sqrt(weights[0])
            return (np.array([(0.0, 0.0)]), np.array([single_radius]))

        # Special case for very few circles
        if n_circles <= 4:
            r_min = math.sqrt(radius**2 / sum(weights))
            radii = r_min * np.sqrt(weights)

            # For few circles, use fixed positions with some randomness
            angles = np.linspace(0, 2 * math.pi, n_circles, endpoint=False)
            pos_MUs = []

            # Place circles at evenly spaced angles, about 2/3 of the way to the edge
            placement_radius = radius * 0.6
            for i in range(n_circles):
                angle = angles[i] + np.random.uniform(
                    -0.2, 0.2
                )  # Small random perturbation
                pos_x = placement_radius * math.cos(angle)
                pos_y = placement_radius * math.sin(angle)
                pos_MUs.append((pos_y, pos_x))

            return (np.array(pos_MUs), radii)

        # Normal case for many circles
        r_min = math.sqrt(radius**2 / sum(weights))
        radii = r_min * np.sqrt(weights)

        # Handle arrays of any size
        if n_circles > 1:
            # Sort in descending order for positioning (larger circles first)
            sorted_indices = np.argsort(-radii)
            radii_sorted = radii[sorted_indices]
        else:
            # For single element, no sorting needed
            sorted_indices = np.array([0])
            radii_sorted = radii

        pos_MUs = []
        for i in range(len(radii_sorted)):
            MAX_ATTEMPTS = 1000  # Prevent infinite loops
            attempts = 0

            while attempts < MAX_ATTEMPTS:
                attempts += 1
                criterion1 = False  ## Inside the muscle circle
                criterion2 = True  ## Doesn't exceed the maximum MU overlap
                theta = np.random.uniform(low=-math.pi, high=math.pi)
                R = np.random.normal(loc=radius / 2, scale=radius / 4)
                pos_x = R * np.cos(theta)
                pos_y = R * np.sin(theta)
                if (
                    math.sqrt(pos_x**2 + pos_y**2) + radii_sorted[i] <= radius
                ):  # should be inside the external circle
                    criterion1 = True
                    for j in range(i):
                        dist = math.sqrt(
                            (pos_MUs[j][0] - pos_x) ** 2 + (pos_MUs[j][1] - pos_y) ** 2
                        )  # check the distance with other motor units

                        # Gradually relax overlap constraint as attempts increase
                        overlap_factor = min(1.0, 0.5 + (attempts / MAX_ATTEMPTS) * 0.5)
                        if (
                            dist
                            < np.maximum(radii_sorted[j], radii_sorted[i])
                            * overlap_factor
                        ):  # if the overlap is bigger than the radius of one of the motor units
                            criterion2 = False
                            break
                if criterion1 and criterion2:  # the stop condition
                    break

            # If we couldn't find a good spot after max attempts, place it anywhere valid
            if attempts >= MAX_ATTEMPTS:
                # Just find a spot inside the muscle circle
                for emergency_attempt in range(100):
                    theta = np.random.uniform(low=-math.pi, high=math.pi)
                    # Try closer to the center to avoid boundary issues
                    R = np.random.uniform(low=0, high=radius / 2)
                    pos_x = R * np.cos(theta)
                    pos_y = R * np.sin(theta)
                    if math.sqrt(pos_x**2 + pos_y**2) + radii_sorted[i] <= radius:
                        break

            pos_MUs.append((pos_y, pos_x))

        # Reorder the results to original order if needed
        if n_circles > 1:
            # Create arrays for reordering
            pos_MUs_reordered = [None] * len(pos_MUs)
            radii_reordered = np.zeros_like(radii)

            # Map back to original indices
            for i, orig_idx in enumerate(sorted_indices):
                pos_MUs_reordered[orig_idx] = pos_MUs[i]
                radii_reordered[orig_idx] = radii_sorted[i]

            return (np.array(pos_MUs_reordered), radii_reordered)
        else:
            # For a single element, no reordering needed
            return (np.array(pos_MUs), radii)

    elif mode == "equal":
        pos_Fibs = []
        Nfib = weights
        fib_radius = radius[1]
        radius = radius[0]
        for i in range(0, Nfib):
            MAX_ATTEMPTS = 1000  # Prevent infinite loops
            attempts = 0

            while attempts < MAX_ATTEMPTS:
                attempts += 1
                criterion1 = False  ## Inside the MU circle
                criterion2 = True  ## Doesn't exceed the maximum fiber overlap
                theta = np.random.uniform(low=-math.pi, high=math.pi)
                R = np.random.normal(loc=radius / 2, scale=radius / 4)
                pos_x = R * np.cos(theta)
                pos_y = R * np.sin(theta)
                if (
                    math.sqrt(pos_x**2 + pos_y**2) + fib_radius <= radius
                ):  # should be inside the external circle
                    criterion1 = True
                    for j in range(0, i):
                        dist = math.sqrt(
                            (pos_Fibs[j][0] - pos_x) ** 2
                            + (pos_Fibs[j][1] - pos_y) ** 2
                        )
                        # Gradually relax overlap constraint for high attempt counts
                        if dist < 2 * fib_radius * min(
                            1.0, 0.7 + (attempts / MAX_ATTEMPTS) * 0.3
                        ):
                            criterion2 = False
                            break
                if criterion1 and criterion2:
                    break

            # If we couldn't find a good spot after max attempts, use a fallback strategy
            if attempts >= MAX_ATTEMPTS:
                # Just place it anywhere in the circle that fits
                for _ in range(100):
                    theta = np.random.uniform(low=-math.pi, high=math.pi)
                    R = np.random.uniform(low=0, high=radius / 2)
                    pos_x = R * np.cos(theta)
                    pos_y = R * np.sin(theta)
                    if math.sqrt(pos_x**2 + pos_y**2) + fib_radius <= radius:
                        break

            pos_Fibs.append((pos_x, pos_y))
        return pos_Fibs

    return None
