#######################################################################################################
##################################### Initial Explanations ############################################
#######################################################################################################

# This script distributes the motor units inside the muscle and the fibers inside each motor unit,
# which are treated as circles. This script support two types of distributions: with unequal (for motor
# units inside the muscle) and equal circle sizes (for fibers inside a motor unit). They are distributed
# according an uniform distribution in the angular position and according an gaussian distribution
# centered in the middle for radial position.


import math
import numpy as np


def distribute_circles(radius, mode, weights):
    """
    Distributes smaller circles within a larger circle.

    Args:
        mode (string):
        'unequal' if smaller circles don't have the same radius.
        'equal' if smaller circles have same radius.

        radius (tuple or float):
        (1) tuple: if mode is 'equal', radius should be a tuple, which 1st position
        should be radius of external circle (float) and the 2nd position
        should be the smaller circles radii.
        (2) float: if model is 'unequal', radius should be the radius of external
        circle (float).

        weights (tuple or float):
        (1) int: if mode is 'equal', weights are the number of circles to be
        distributed
        (2) array: if model is 'unequal', weights is an array with the weight of
        each circle area


    Returns (tuple or list):
        (1) array: if mode is 'equal', the function returns an array, which
        contains the positions of each circle (tuple).
        (2): if model is 'unequal', the function returns a tuple, which 1st position
        contains an array with the positions of each circle (2d array) and the 2nd
        position contains an array of the radii of each circle (float)
    """
    if mode == "unequal":  ## circles to be distributed don't have the same radius
        r_min = math.sqrt(radius**2 / sum(weights))
        radii = r_min * np.sqrt(weights)
        radii = np.flip(radii)  # Sort in descending order
        pos_MUs = []
        for i in range(0, len(radii)):
            while True:  # 3 this is a do{}while() loop
                criterion1 = False  ## Inside the muscle circle
                criterion2 = True  ## Doesn't exceed the maximum MU overlap
                theta = np.random.uniform(low=-math.pi, high=math.pi)
                R = np.random.normal(loc=radius / 2, scale=radius / 4)
                pos_x = R * np.cos(theta)
                pos_y = R * np.sin(theta)
                if (
                    math.sqrt(pos_x**2 + pos_y**2) + radii[i] <= radius
                ):  # should be inside the external circle
                    criterion1 = True
                    for j in range(0, i):
                        dist = math.sqrt(
                            (pos_MUs[j][0] - pos_x) ** 2 + (pos_MUs[j][1] - pos_y) ** 2
                        )  # check the distance with other motor units
                        if (
                            dist < np.maximum(radii[j], radii[i])
                        ):  # if the overlap is bigger than the radius of one of the motor units
                            criterion2 = False
                            break
                if criterion1 and criterion2:  # the stop condition
                    break
            pos_MUs.append(
                (pos_y, pos_x)
            )  # It will be flipped back to x, y in the next line
        return (np.flip(pos_MUs), np.flip(radii))  # Sort in ascending order
    if mode == "equal":
        pos_Fibs = []
        Nfib = weights
        fib_radius = radius[1]
        radius = radius[0]
        for i in range(0, Nfib):
            while True:
                criterion1 = False  ## Inside the MU circle
                criterion2 = True  ## Doesn't exceed the maximum fiber overlap
                theta = np.random.uniform(low=-math.pi, high=math.pi)
                R = np.random.normal(loc=radius / 2, scale=radius / 4)
                pos_x = R * np.cos(theta)
                pos_y = R * np.sin(theta)
                if (
                    math.sqrt(pos_x**2 + pos_y**2) + fib_radius <= radius
                ):  # should be inside the external circle
                    criterion1 = True
                    for j in range(0, i):
                        dist = math.sqrt(
                            (pos_Fibs[j][0] - pos_x) ** 2
                            + (pos_Fibs[j][1] - pos_y) ** 2
                        )
                        if dist < 2 * fib_radius:  ## they can't have any overlap
                            criterion2 = False
                            break
                if criterion1 and criterion2:
                    break
            pos_Fibs.append((pos_x, pos_y))
        return pos_Fibs
    return None
