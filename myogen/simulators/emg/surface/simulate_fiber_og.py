#######################################################################################################
##################################### Initial Explanations ############################################
#######################################################################################################

# This script simulates a single fiber: it must be runned in the script simulate_muscle_XXX.py, which simulates
# this script several times and generates the MUAPs for each MU dectected at each electrode channel.
# Length parameters are in mm, frequencies in kHz, time in ms, conductivities in S/m


#######################################################################################################
##################################### Input Parameters ################################################
#######################################################################################################

# Fs             -> Sampling frequency
# v              -> Conduction velocity
# N              -> Number of points in t and z domains
# M              -> Number of points in theta domain
# r              -> Model total radius
# r_bone         -> Bone radius
# th_fat         -> Fat thickness
# th_skin        -> Skin thickness
# R              -> Source position in rho coordinate
# L1             -> Semifiber length (z > 0)
# L2             -> Semifiber length (z < 0)
# zi             -> Innervation zone (z = 0 for the mean innervation zone of the M.U)
# alpha          -> Inclination angle (in degrees) between the eletrode matrix and the muscle fibers
# channels       -> Matrix of electrodes (tuple). The columns are aligned with the muscle fibers if alpha = 0
# center         -> Matrix of electrodes center (tuple -> (z center in mm, theta center in degrees))
# d_ele          -> Distance between neighboring electrodes
# rele           -> Electrode radius (circular electrodes)
# sig_bone       -> Bone conductivity
# sig_muscle_rho -> Muscle conductivity in rho direction
# sig_muscle_z   -> Muscle conductivity in z direction
# sig_fat        -> Fat conductivity
# sig_skin       -> Skin conductivity


#######################################################################################################
##################################### Model ###########################################################
#######################################################################################################

import math
import numpy as np
from scipy.special import kv as Kn
from scipy.special import iv as In
from scipy.special import jv as Jn


def simulate_fiber(
    Fs,
    v,
    N,
    M,
    r,
    r_bone,
    th_fat,
    th_skin,
    R,
    L1,
    L2,
    zi,
    alpha,
    channels,
    center,
    d_ele,
    rele,
    sig_muscle_rho,
    sig_muscle_z,
    sig_fat,
    sig_skin,
    sig_bone=0,
):
    ###################################################################################################
    ## 1. Constants

    ## Model angular frequencies
    k_theta = np.linspace(-(M - 1) / 2, (M - 1) / 2, M)
    k_t = 2 * math.pi * np.linspace(-Fs / 2, Fs / 2, N)
    k_z = k_t / v  ## v = z/t => v = (1/k_z)/(1/k_t) => v = k_t/k_z
    (kt_mesh_kzkt, kz_mesh_kzkt) = np.meshgrid(k_t, k_z)

    ## Model radii -> (Farina, 2004), Figure 1 - b)
    th_muscle = r - th_fat - th_skin - r_bone
    a = r_bone
    b = r_bone + th_muscle
    c = r_bone + th_muscle + th_fat
    d = r_bone + th_muscle + th_fat + th_skin

    ###################################################################################################
    ## 2. I(k_t, k_z)

    A = 96  ## mV/mm^3 -> (Farina, 2001), eq (16)
    z = np.linspace(
        -(N - 1) * (v / Fs) / 2, (N - 1) * (v / Fs) / 2, N
    )  ## Ts(z) = Ts(t)*v = v/Fs(t)
    aux = np.multiply(
        A * np.exp(-z), 3 * z**2 - z**3
    )  ## (Farina, 2001), eq (16) -> derivative of Vm(z)
    for i in range(0, len(z)):
        if z[i] < 0:
            aux[i] = 0
    psi = -f_minus_t(aux)  ## derivative of Vm(-z) with respect to z
    PSI = np.fft.fftshift(np.fft.fft(psi)) / len(psi)
    PSI_conj = np.conj(PSI)
    PSI_conj = PSI_conj.reshape(-1, len(PSI_conj))
    ones = np.ones((len(PSI_conj[0, :]), 1))
    PSI_mesh_conj = np.dot(ones, PSI_conj)
    I_kzkt = 1j * np.multiply(
        kz_mesh_kzkt / v, np.multiply(PSI_mesh_conj, np.exp(-1j * kz_mesh_kzkt * zi))
    )
    k_eps = kz_mesh_kzkt + kt_mesh_kzkt / v
    k_beta = kz_mesh_kzkt - kt_mesh_kzkt / v
    aux1 = np.multiply(
        np.exp(-1j * k_eps * L1 / 2), np.sinc(k_eps * L1 / 2 / math.pi) * L1
    )
    aux2 = np.multiply(
        np.exp(1j * k_beta * L2 / 2), np.sinc(k_beta * L2 / 2 / math.pi) * L2
    )
    I_kzkt = np.multiply(I_kzkt, (aux1 - aux2))  ## (Farina, 2001), eq (22)
    t = np.linspace(0, (N - 1) / Fs, N)

    ###################################################################################################
    ## 2. H_vc(k_z, k_theta)

    H_vc = np.zeros((len(k_z), len(k_theta)))
    am = a * math.sqrt(sig_muscle_z / sig_muscle_rho)  ## (Farina, 2004), eq (19)
    bm = b * math.sqrt(sig_muscle_z / sig_muscle_rho)
    Rm = R * math.sqrt(sig_muscle_z / sig_muscle_rho)
    for i in range(int(len(k_z) / 2), len(k_z)):
        for j in range(int(len(k_theta) / 2), len(k_theta)):
            ## (Farina, 2004), eq (18)
            # Matrix A
            # First Row
            A = np.zeros((7, 7))
            A[0, 0] = In(k_theta[j], a * k_z[i]) / In(k_theta[j], a * k_z[i])
            A[0, 1] = -In(k_theta[j], am * k_z[i]) / In(k_theta[j], bm * k_z[i])
            A[0, 2] = -Kn(k_theta[j], am * k_z[i]) / Kn(k_theta[j], bm * k_z[i])
            # Second Row
            A[1, 0] = (
                sig_bone
                / (In(k_theta[j], a * k_z[i]))
                * (In(k_theta[j] + 1, a * k_z[i]) + In(k_theta[j] - 1, a * k_z[i]))
                / 2
            )
            A[1, 1] = (
                -math.sqrt(sig_muscle_rho * sig_muscle_z)
                / In(k_theta[j], bm * k_z[i])
                * (In(k_theta[j] + 1, am * k_z[i]) + In(k_theta[j] - 1, am * k_z[i]))
                / 2
            )
            A[1, 2] = (
                -math.sqrt(sig_muscle_rho * sig_muscle_z)
                / Kn(k_theta[j], bm * k_z[i])
                * (-1)
                * (Kn(k_theta[j] + 1, am * k_z[i]) + Kn(k_theta[j] - 1, am * k_z[i]))
                / 2
            )
            # Third Row
            A[2, 1] = In(k_theta[j], bm * k_z[i]) / In(k_theta[j], bm * k_z[i])
            A[2, 2] = Kn(k_theta[j], bm * k_z[i]) / Kn(k_theta[j], bm * k_z[i])
            A[2, 3] = -In(k_theta[j], b * k_z[i]) / In(k_theta[j], c * k_z[i])
            A[2, 4] = -Kn(k_theta[j], b * k_z[i]) / Kn(k_theta[j], c * k_z[i])
            # Fourth Row
            A[3, 1] = (
                math.sqrt(sig_muscle_rho * sig_muscle_z)
                / In(k_theta[j], bm * k_z[i])
                * (In(k_theta[j] + 1, bm * k_z[i]) + In(k_theta[j] - 1, bm * k_z[i]))
                / 2
            )
            A[3, 2] = (
                math.sqrt(sig_muscle_rho * sig_muscle_z)
                / Kn(k_theta[j], bm * k_z[i])
                * (-1)
                * (Kn(k_theta[j] + 1, bm * k_z[i]) + Kn(k_theta[j] - 1, bm * k_z[i]))
                / 2
            )
            A[3, 3] = (
                -sig_fat
                / In(k_theta[j], c * k_z[i])
                * (In(k_theta[j] + 1, b * k_z[i]) + In(k_theta[j] - 1, b * k_z[i]))
                / 2
            )
            A[3, 4] = (
                -sig_fat
                / Kn(k_theta[j], c * k_z[i])
                * (-1)
                * (Kn(k_theta[j] + 1, b * k_z[i]) + Kn(k_theta[j] - 1, b * k_z[i]))
                / 2
            )
            # Fifth Row
            A[4, 3] = In(k_theta[j], c * k_z[i]) / In(k_theta[j], c * k_z[i])
            A[4, 4] = Kn(k_theta[j], c * k_z[i]) / Kn(k_theta[j], c * k_z[i])
            A[4, 5] = -In(k_theta[j], c * k_z[i]) / In(k_theta[j], d * k_z[i])
            A[4, 6] = -Kn(k_theta[j], c * k_z[i]) / Kn(k_theta[j], d * k_z[i])
            # Sixth Row
            A[5, 3] = (
                sig_fat
                / In(k_theta[j], c * k_z[i])
                * (In(k_theta[j] + 1, c * k_z[i]) + In(k_theta[j] - 1, c * k_z[i]))
                / 2
            )
            A[5, 4] = (
                sig_fat
                / Kn(k_theta[j], c * k_z[i])
                * (-1)
                * (Kn(k_theta[j] + 1, c * k_z[i]) + Kn(k_theta[j] - 1, c * k_z[i]))
                / 2
            )
            A[5, 5] = (
                -sig_skin
                / In(k_theta[j], d * k_z[i])
                * (In(k_theta[j] + 1, c * k_z[i]) + In(k_theta[j] - 1, c * k_z[i]))
                / 2
            )
            A[5, 6] = (
                -sig_skin
                / Kn(k_theta[j], d * k_z[i])
                * (-1)
                * (Kn(k_theta[j] + 1, c * k_z[i]) + Kn(k_theta[j] - 1, c * k_z[i]))
                / 2
            )
            # Seventh Row
            A[6, 5] = (
                sig_skin
                / In(k_theta[j], d * k_z[i])
                * (In(k_theta[j] + 1, d * k_z[i]) + In(k_theta[j] - 1, d * k_z[i]))
                / 2
            )
            A[6, 6] = (
                sig_skin
                / Kn(k_theta[j], d * k_z[i])
                * (-1)
                * (Kn(k_theta[j] + 1, d * k_z[i]) + Kn(k_theta[j] - 1, d * k_z[i]))
                / 2
            )
            # Vector b
            B = np.zeros((7, 1))
            B[0, 0] = (
                In(k_theta[j], am * k_z[i])
                * Kn(k_theta[j], Rm * k_z[i])
                / sig_muscle_rho
            )
            B[1, 0] = (
                math.sqrt(sig_muscle_z / sig_muscle_rho)
                * (In(k_theta[j] + 1, am * k_z[i]) + In(k_theta[j] - 1, am * k_z[i]))
                / 2
                * Kn(k_theta[j], Rm * k_z[i])
            )
            B[2, 0] = (
                -In(k_theta[j], Rm * k_z[i])
                * Kn(k_theta[j], bm * k_z[i])
                / sig_muscle_rho
            )
            B[3, 0] = (
                -math.sqrt(sig_muscle_z / sig_muscle_rho)
                * In(k_theta[j], Rm * k_z[i])
                * (-1)
                * (Kn(k_theta[j] + 1, bm * k_z[i]) + Kn(k_theta[j] - 1, bm * k_z[i]))
                / 2
            )
            # Vector X
            if r_bone == 0:
                A = A[2:, 2:]
                B = B[2:]
                X = np.dot(np.linalg.inv(A), B)
                H_vc[i, j] = X[3, 0] + X[4, 0]
            else:
                X = np.dot(np.linalg.inv(A), B)
                H_vc[i, j] = X[5, 0] + X[6, 0]
            H_vc[i, j - 1 - 2 * (j - int(len(k_theta) / 2))] = H_vc[
                i, j
            ]  ## H_vc is symmetric in k_theta and k_z
            H_vc[i - 1 - 2 * (i - int(len(k_z) / 2)), j] = H_vc[i, j]
            H_vc[
                i - 1 - 2 * (i - int(len(k_z) / 2)),
                j - 1 - 2 * (j - int(len(k_theta) / 2)),
            ] = H_vc[i, j]

    ###################################################################################################
    ## 3. H_ele(k_z, k_theta)

    ## H_sf
    (ktheta_mesh_kzktheta, kz_mesh_kzktheta) = np.meshgrid(k_theta, k_z)
    # Rotation Angle -> (Farina, 2004), eq 36
    alpha = alpha * math.pi / 180
    kz_mesh_kzktheta_new = ktheta_mesh_kzktheta / r * math.sin(
        alpha
    ) + kz_mesh_kzktheta * math.cos(alpha)
    ktheta_mesh_kzktheta_new = ktheta_mesh_kzktheta * math.cos(
        alpha
    ) - kz_mesh_kzktheta * r * math.sin(alpha)
    # Spatial Filter - single differential
    # H_sf = np.exp(1j*kz_mesh_kzktheta_new)-np.exp(-1j*kz_mesh_kzktheta_new)
    # Spatial Filter - monopolar
    H_sf = 1

    ## H_size -> (Farina, 2004), eq (33)
    arg = np.sqrt(
        (rele * ktheta_mesh_kzktheta / r) ** 2 + (rele * kz_mesh_kzktheta) ** 2
    )
    H_size = 2 * np.divide(Jn(1, arg), arg)
    auxxx = np.ones(H_size.shape)
    H_size[np.isnan(H_size)] = auxxx[np.isnan(H_size)]

    ## H_ele
    H_ele = np.multiply(H_sf, H_size)

    ###################################################################################################
    ## 4. H_glo(k_z, k_theta)

    ## Detection system
    # pos_z
    pos_z = np.zeros((channels[0], channels[1]))
    if channels[0] % 2 == 1:
        index_center = int((channels[0] - 1) / 2)
        pos_z[index_center, :] = center[0]
        for i in range(1, index_center + 1):
            pos_z[index_center + i, :] = pos_z[index_center + i - 1, :] + d_ele
            pos_z[index_center - i, :] = pos_z[index_center - i + 1, :] - d_ele
    else:
        index_center1 = int(channels[0] / 2)
        index_center2 = index_center1 - 1
        pos_z[index_center1, :] = center[0] + d_ele / 2
        pos_z[index_center2, :] = center[0] - d_ele / 2
        for i in range(1, index_center2 + 1):
            pos_z[index_center1 + i, :] = pos_z[index_center1 + i - 1, :] + d_ele
            pos_z[index_center2 - i, :] = pos_z[index_center2 - i + 1, :] - d_ele
    # pos_theta
    pos_theta = np.zeros((channels[0], channels[1]))
    if channels[1] % 2 == 1:
        index_center = int((channels[1] - 1) / 2)
        pos_theta[:, index_center] = center[1] * math.pi / 180
        for i in range(1, index_center + 1):
            pos_theta[:, index_center + i] = (
                pos_theta[:, index_center + i - 1] + d_ele / r
            )
            pos_theta[:, index_center - i] = (
                pos_theta[:, index_center - i + 1] - d_ele / r
            )
    else:
        index_center1 = int(channels[1] / 2)
        index_center2 = index_center1 - 1
        pos_theta[:, index_center1] = center[1] * math.pi / 180 + d_ele / 2 / r
        pos_theta[:, index_center2] = center[1] * math.pi / 180 - d_ele / 2 / r
        for i in range(1, index_center2 + 1):
            pos_theta[:, index_center1 + i] = (
                pos_theta[:, index_center1 + i - 1] + d_ele / r
            )
            pos_theta[:, index_center2 - i] = (
                pos_theta[:, index_center2 - i + 1] - d_ele / r
            )

    ## Rotated detection system (Farina, 2004), eq (36)
    displacement = center[0] * np.ones(pos_z.shape)
    pos_z_new = (
        -r * math.sin(alpha) * pos_theta
        + math.cos(alpha) * (pos_z - displacement)
        + displacement
    )
    pos_theta_new = (
        math.cos(alpha) * pos_theta + math.sin(alpha) * (pos_z - displacement) / r
    )
    pos_z = pos_z_new
    pos_theta = pos_theta_new
    H_glo = np.multiply(H_vc, H_ele)
    B_kz = np.zeros((channels[0], channels[1], len(k_z)), dtype="complex_")
    for channel_z in range(0, channels[0]):
        for channel_theta in range(0, channels[1]):
            ## B_kz -> (Farina, 2004), eq (5)
            arg = np.multiply(
                H_glo,
                np.exp(1j * pos_theta[channel_z, channel_theta] * ktheta_mesh_kzktheta)
                * (k_theta[1] - k_theta[0]),
            )
            B_kz[channel_z, channel_theta, :] = sum(np.transpose(arg)) / 2 / math.pi

    ###################################################################################################
    ## 5. phi(t) for each channel

    ## (Farina, 2004), eq (2)
    phi = np.zeros((channels[0], channels[1], len(t)))
    for channel_z in range(0, channels[0]):
        for channel_theta in range(0, channels[1]):
            auxiliar = np.dot(
                np.ones((len(I_kzkt[1, :]), 1)),
                B_kz[channel_z, channel_theta, :].reshape(1, -1),
            )
            auxiliar = np.transpose(auxiliar)
            arg = np.multiply(I_kzkt, auxiliar)
            arg2 = np.multiply(
                arg,
                np.exp(1j * pos_z[channel_z, channel_theta] * kz_mesh_kzkt)
                * (k_z[1] - k_z[0]),
            )
            PHI = sum(arg2)
            phi[channel_z, channel_theta, :] = np.real(
                (np.fft.ifft(np.fft.fftshift(PHI / 2 / math.pi * len(psi))))
            )
    return phi


def f_minus_t(y):
    y_new = np.zeros(len(y))
    for i in range(0, len(y)):
        y_new[i] = y[-i]
    return y_new


#######################################################################################################
###################################### References #####################################################
#######################################################################################################

# FARINA, D.; MERLETTI, R. A novel approach for precise simulation of the EMG signal detected by surface electrodes.
# IEEE Transactions on Biomedical Engineering, v. 48, n. 6, p. 637–646, 2001. DOI: 10.1109/10.923782.

# FARINA, D.; MESIN, L.; MARTINA, S.; MERLETTI, R. A Surface EMG Generation Model With Multilayer Cylindrical
# Description of the Volume Conductor. IEEE Transactions on Biomedical Engineering, v. 51, n. 3, p. 415–426,
# 2004. DOI: 10.1109/TBME.2003.820998.
