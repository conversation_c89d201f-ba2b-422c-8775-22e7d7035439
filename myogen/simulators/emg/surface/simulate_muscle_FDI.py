#######################################################################################################
##################################### Initial Explanations ############################################
#######################################################################################################
# This script simulates the FDI muscle: it simulates the script simulate_fiber.py several times and generates
# the MUAPs for each MU dectected at each electrode channel. It calls the script Distribute_circles.py to
# distributes the motor units inside the muscle and the fibers inside each motor unit.

import math
import warnings
from pathlib import Path
from typing import Tuple, List

import numpy as np
from tqdm import tqdm

from myogen.simulators.emg.surface import MUAP_SHAPE__TENSOR
from myogen.simulators.emg.surface.distribute_circles_vibed import distribute_circles
from myogen.simulators.emg.surface.simulate_fiber_og import simulate_fiber

warnings.filterwarnings("ignore")
from datetime import datetime


def generate_fdi_muscle_muaps(
    save__path: Path,
    Fs: float = 2.048,
    v: float = 4.1,
    N: int = 256,
    M: int = 180,
    N_MU: int = 10,
    N_fib_min: int = 21,
    N_fib_max: int = 1764,
    mean_fib_radius: float = 16e-3,
    mean_fib_len: float = 32,
    var_fib_len: float = 3,
    A: float = 150,
    r_bone: float = 0,
    th_fat: float = 1,
    th_skin: float = 1,
    alpha: float = 0,
    sig_muscle_rho: float = 0.09,
    sig_muscle_z: float = 0.4,
    sig_fat: float = 0.041,
    sig_skin: float = 1,
    channels: Tuple[int, int] = (13, 5),
    d_ele: float = 4,
    rele: float = 0.75,
) -> Tuple[
    MUAP_SHAPE__TENSOR,
    List[Tuple[float, float]],
    List[float],
    List[List[Tuple[float, float]]],
]:
    """
    Simulate the FDI muscle by generating MUAPs for each motor unit detected at each electrode channel.

    Parameters
    ----------
    save__path : Path
        Path to save the results
    Fs : float, optional
        Sampling frequency, by default 2.048
    v : float, optional
        Mean conduction velocity, by default 4.1 (Botelho, 2019)
    N : int, optional
        Number of points in t and z domains, by default 256 (arbitrary)
    M : int, optional
        Number of points in theta domain, by default 180 (arbitrary)
    N_MU : int, optional
        Number of motor units, by default 10 (Feinstein, 1955)
    N_fib_min : int, optional
        Minimum number of fibers per motor unit, by default 21 (Feinstein, 1955)
    N_fib_max : int, optional
        Maximum number of fibers per motor unit, by default 1764 (Feinstein, 1955)
    mean_fib_radius : float, optional
        Mean fiber radius in mm, by default 16e-3 (Hwang, 2012)
    mean_fib_len : float, optional
        Mean fiber length in mm, by default 32 (Jacobson, 1992)
    var_fib_len : float, optional
        Fiber length variation, by default 3 (Jacobson, 1992)
    A : float, optional
        Cross-sectional area, by default 150 (Jacobson, 1992)
    r_bone : float, optional
        Bone radius, by default 0
    th_fat : float, optional
        Fat thickness, by default 1 (Storchle, 2018)
    th_skin : float, optional
        Skin thickness, by default 1 (Brodar, 1960)
    alpha : float, optional
        Alpha parameter, by default 0
    sig_muscle_rho : float, optional
        Muscle conductivity in radial direction, by default 0.09 (Botelho, 2019)
    sig_muscle_z : float, optional
        Muscle conductivity in z direction, by default 0.4 (Botelho, 2019)
    sig_fat : float, optional
        Fat conductivity, by default 0.041 (Botelho, 2019)
    sig_skin : float, optional
        Skin conductivity, by default 1 (Roeleveld, 1997)
    channels : Tuple[int, int], optional
        Electrode matrix dimensions, by default (13, 5) (ELSCH064NM4)
    d_ele : float, optional
        Electrode diameter, by default 4 (ELSCH064NM4)
    rele : float, optional
        Electrode radius, by default 0.75 (approximate value)

    Returns
    -------
    Tuple[MUAP_SHAPE__TENSOR, List[Tuple[float, float]], List[float], List[List[Tuple[float, float]]]]
        A tuple containing:
        - MUAP tensor of shape (n_muaps, n_rows, n_cols, n_samples)
        - List of motor unit positions
        - List of motor unit radii
        - List of fiber positions for each motor unit

    References
    ----------
    Botelho, D., Curran, K., & Lowery, M. M. (2019). Anatomically accurate model of EMG during index finger flexion and abduction derived from diffusion tensor imaging. PLOS Computational Biology, 15(8), e1007267.

    Brodar, V. (1960). Observations on Skin Thickness and Subcutaneous Tissue in Man. Zeitschrift Für Morphologie Und Anthropologie, 50(3), 386–95.

    Feinstein, B., Lindegard, B., Nyman, E., & Wohlfart, G. (1955). MORPHOLOGIC STUDIES OF MOTOR UNITS IN NORMAL HUMAN MUSCLES. Cells Tissues Organs, 23(2), 127–142.

    Hwang, K., Huan, F., & Kim, D. J. (2012). Muscle fibre types of the lumbrical, interossei, flexor, and extensor muscles moving the index finger. Journal of Plastic Surgery and Hand Surgery, 47(4), 268–272.

    Jacobson, M. D., Raab, R., Fazeli, B. M., Abrams, R. A., Botte, M. J., & Lieber, R. L. (1992). Architectural design of the human intrinsic hand muscles. The Journal of Hand Surgery, 17(5), 804–809.

    Roeleveld, K., Blok, J. H., Stegeman, D. F., & van Oosterom, A. (1997). Volume conduction models for surface EMG; confrontation with measurements. Journal of Electromyography and Kinesiology, 7(4), 221–232.

    Störchle, P., Müller, W., Sengeis, M., Lackner, S., Holasek, S., & Fürhapter-Rieger, A. (2018). Measurement of mean subcutaneous fat thickness: eight standardised ultrasound sites compared to 216 randomly selected sites. Scientific Reports, 8(1), 16268.
    """
    start = datetime.now()

    # Calculate muscle radius from cross-sectional area
    r = math.sqrt(A / math.pi)
    var_zi = mean_fib_len / 10  # (Botelho, 2019)

    # Calculate number of fibers per motor unit
    index = np.linspace(1, N_MU, N_MU)
    Nfib_MUs = N_fib_min * np.exp(index * (np.log(N_fib_max / N_fib_min) / N_MU))
    Nfib_MUs = np.floor(Nfib_MUs)

    # Distribute motor units inside muscle
    pos_MUs, r_MUs = distribute_circles(radius=r, mode="unequal", weights=Nfib_MUs)

    # Initialize arrays
    t = np.linspace(0, (N - 1) / Fs, N)
    MUAP = np.zeros((N_MU, channels[0], channels[1], len(t)))
    all_pos_Fibs = []

    # Simulate each motor unit
    for i in range(N_MU):
        Nfib = int(Nfib_MUs[i])

        # Distribute fibers inside motor unit
        pos_Fibs = distribute_circles(
            radius=(r_MUs[i], mean_fib_radius), mode="equal", weights=Nfib
        )
        all_pos_Fibs.append(pos_Fibs)

        zi = np.random.uniform(low=-var_zi / 2, high=var_zi / 2)

        # Simulate each fiber
        for j in tqdm(
            range(Nfib),
            desc=f"Simulating fibers for MU {str(i + 1).zfill(len(str(N_MU)))}/{N_MU}",
        ):
            pos_fib = (pos_MUs[i][0] + pos_Fibs[j][0], pos_MUs[i][1] + pos_Fibs[j][1])
            R = np.sqrt(pos_fib[0] ** 2 + pos_fib[1] ** 2)

            # Calculate theta
            if pos_fib[0] == 0:
                theta = np.arctan(pos_fib[1] * np.inf)
            else:
                theta = np.arctan(pos_fib[1] / pos_fib[0])
            if pos_fib[0] < 0:
                theta = theta + math.pi

            # Calculate fiber length and distances
            fib_len = mean_fib_len + np.random.uniform(
                low=-var_fib_len, high=var_fib_len
            )
            L2 = abs(zi - fib_len / 2)
            L1 = abs(zi + fib_len / 2)
            center = (0, -theta / math.pi * 180)

            # Simulate fiber and add to MUAP
            phi = simulate_fiber(
                Fs=Fs,
                v=v,
                N=N,
                M=M,
                r=r,
                r_bone=r_bone,
                th_fat=th_fat,
                th_skin=th_skin,
                R=R,
                L1=L1,
                L2=L2,
                zi=zi,
                alpha=alpha,
                channels=channels,
                center=center,
                d_ele=d_ele,
                rele=rele,
                sig_muscle_rho=sig_muscle_rho,
                sig_muscle_z=sig_muscle_z,
                sig_skin=sig_skin,
                sig_fat=sig_fat,
            )
            MUAP[i] = MUAP[i] + phi

    print(f"Simulation completed in {datetime.now() - start}")

    np.save(save__path / "MUAPs.npy", MUAP)

    return MUAP, pos_MUs, r_MUs, all_pos_Fibs


if __name__ == "__main__":
    from myogen.utils.plotting import plot_muscle_distribution

    save__path = Path("FDI_MUAPs")
    save__path.mkdir(exist_ok=True)

    # Run simulation with default parameters
    MUAP, pos_MUs, r_MUs, all_pos_Fibs = generate_fdi_muscle_muaps(
        save__path=save__path,
        N_MU=3,
    )

    # Plot the muscle distribution
    plot_muscle_distribution(
        save__path=save__path,
        pos_MUs=pos_MUs,
        r_MUs=r_MUs,
        r=math.sqrt(150 / math.pi),
        pos_Fibs=all_pos_Fibs,
        mean_fib_radius=16e-3,
    )


#######################################################################################################
###################################### References #####################################################
#######################################################################################################

# BOTELHO, Diego; CURRAN, Kathleen; LOWERY, Madeleine M. Anatomically accurate
# model of EMG during index finger flexion and abduction derived from diffusion tensor
# imaging. PLOS Computational Biology, [S. l.], v. 15, n. 8, p. e1007267, 2019. DOI:
# 10.1371/journal.pcbi.1007267.

# BRODAR, Vida. Observations on Skin Thickness and Subcutaneous Tissue in Man. Zeitschrift
# Für Morphologie Und Anthropologie, [S. l.], v. 50, n. 3, p. 386–95, 1960. Available in:
# https://about.jstor.org/terms

# ENOKA, Roger M.; FUGLEVAND, Andrew J. Motor unit physiology: Some unresolved issues. Muscle & Nerve,
# [S. l.], v. 24, n. 1, p. 4–17, 2001. DOI: 10.1002/1097-4598(200101)24:1<4::AID-MUS13>3.0.CO;2-F.

# FEINSTEIN, Bertram; LINDEGARD, Bengt; NYMAN, Eberhard; WOHLFART, Gunnar.
# MORPHOLOGIC STUDIES OF MOTOR UNITS IN NORMAL HUMAN MUSCLES. Cells Tissues
# Organs, [S. l.], v. 23, n. 2, p. 127–142, 1955. DOI: 10.1159/000140989.

# HWANG, Kun; HUAN, Fan; KIM, Dae Joong. Muscle fibre types of the lumbrical, interossei,
# flexor, and extensor muscles moving the index finger. Journal of Plastic Surgery and Hand
# Surgery, [S. l.], v. 47, n. 4, p. 268–272, 2013. DOI: 10.3109/2000656X.2012.755988.

# JACOBSON, Mark D.; RAAB, Rajnik; FAZELI, Babak M.; ABRAMS, Reid A.; BOTTE, Michael J.;
# LIEBER, Richard L. Architectural design of the human intrinsic hand muscles. The Journal of
# Hand Surgery, [S. l.], v. 17, n. 5, p. 804–809, 1992. DOI: 10.1016/0363-5023(92)90446-V.

# ROELEVELD, K.; BLOK, J. H.; STEGEMAN, D. F.; VAN OOSTEROM, A. Volume conduction
# models for surface EMG; confrontation with measurements. Journal of Electromyography
# and Kinesiology, [S. l.], v. 7, n. 4, p. 221–232, 1997. DOI: 10.1016/S1050-6411(97)00009-6.

# STÖRCHLE, Paul; MÜLLER, Wolfram; SENGEIS, Marietta; LACKNER, Sonja; HOLASEK, Sandra;
# FÜRHAPTER-RIEGER, Alfred. Measurement of mean subcutaneous fat thickness: eight
# standardised ultrasound sites compared to 216 randomly selected sites. Scientific Reports,
# [S. l.], v. 8, n. 1, p. 16268, 2018. DOI: 10.1038/s41598-018-34213-0.
