from pathlib import Path

import cupy
import numpy as np
from scipy.signal import resample
from tqdm import tqdm

from myogen import generate_spike_trains, generate_fdi_muscle_muaps
from myogen.simulators.emg.surface import MUAP_SHAPE__TENSOR
from myogen.simulators.spike_train import SPIKE_TRAIN__MATRIX, INPUT_CURRENT__MATRIX


def simulate_surface_emg(
    save__path: Path,
    input_current__matrix: INPUT_CURRENT__MATRIX,
    spike_trains__matrix: SPIKE_TRAIN__MATRIX | None = None,
    muap_shapes__tensor: MUAP_SHAPE__TENSOR | None = None,
    timestep__ms: float = 0.05,
    muap_sampling_frequency: int = 2048,
):
    """
    Simulate surface EMG.

    The spike trains and muap shapes can be provided or generated if missing.

    Parameters
    ----------
    save__path: Path
        Path to save the simulated EMG data.
    input_current__matrix: INPUT_CURRENT__MATRIX | None
        Input current matrix for the simulation.
        If spike_trains__matrix is provided, the input current matrix will be ignored.
    spike_trains__matrix: SPIKE_TRAIN__MATRIX | None
        Spike trains matrix for the simulation.
        If not provided the input_current__matrix must be provided, so that the spike trains will be generated.
    muap_shapes__tensor: MUAP_SHAPE__TENSOR | None
        MUAP shapes tensor for the simulation.
        If not provided the muap shapes will be generated.
    timestep__ms: float
        Simulation timestep in milliseconds.
        Will be used to resample the MUAP shapes to match the simulation timestep.
    muap_sampling_frequency: int
        Sampling frequency for the MUAP shapes in Hz.
        This is used to resample the MUAP shapes to match the simulation timestep.

    Returns
    -------
    surface_emg__tensor: SURFACE_EMG__TENSOR
        Simulated EMG tensor.
    spike_trains__matrix: SPIKE_TRAIN__MATRIX
        Spike trains matrix.
    muap_shapes__tensor: MUAP_SHAPE__TENSOR
        MUAP shapes tensor.
    """
    if spike_trains__matrix is None:
        spike_trains__matrix = generate_spike_trains(
            save__path=save__path, input_current__matrix=input_current__matrix
        )
    if muap_shapes__tensor is None:
        muap_shapes__tensor, _, _, _ = generate_fdi_muscle_muaps(save__path=save__path)

    muap_shapes__tensor = resample(
        muap_shapes__tensor,
        int(
            (muap_shapes__tensor.shape[-1] / muap_sampling_frequency)
            // (timestep__ms / 1000)
        ),
        axis=-1,
    )

    n_pools = spike_trains__matrix.shape[0]
    n_rows = muap_shapes__tensor.shape[1]
    n_cols = muap_shapes__tensor.shape[2]

    # Initialize the result array with the correct shape
    # The shape will depend on the length of the convolution result
    sample_conv = np.convolve(
        spike_trains__matrix[0, 0], muap_shapes__tensor[0, 0, 0], mode="same"
    )

    # Perform the convolution and summation using GPU acceleration
    spike_gpu = cupy.asarray(spike_trains__matrix)
    muap_gpu = cupy.asarray(muap_shapes__tensor)
    surface_emg_gpu = cupy.zeros((n_pools, n_rows, n_cols, len(sample_conv)))

    for mu_pool_idx in tqdm(range(n_pools), desc="Simulating surface EMG"):
        for row_idx in range(n_rows):
            for col_idx in range(n_cols):
                # Process all MUAPs for this combination on GPU
                convolutions = cupy.array(
                    [
                        cupy.correlate(
                            spike_gpu[mu_pool_idx, muap_idx],
                            muap_gpu[muap_idx, row_idx, col_idx],
                            mode="same",
                        )
                        for muap_idx in range(muap_shapes__tensor.shape[0])
                    ]
                )
                # Sum across MUAPs on GPU
                surface_emg_gpu[mu_pool_idx, row_idx, col_idx] = cupy.sum(
                    convolutions, axis=0
                )

    # Transfer results back to CPU
    surface_emg__tensor = cupy.asnumpy(surface_emg_gpu)

    # Save the results
    np.save(save__path / "surface_emg_per_pool.npy", surface_emg__tensor)
    return surface_emg__tensor, spike_trains__matrix, muap_shapes__tensor
