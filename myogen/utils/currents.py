from typing import Op<PERSON>, <PERSON><PERSON>, Union, List

import numpy as np

from myogen.simulators.spike_train import INPUT_CURRENT__MATRIX


def _validate_parameter_list(
    param: Union[float, Tuple[float, float], List[float]], n_pools: int, param_name: str
) -> List[float]:
    """Helper function to validate and convert parameters to a list of values.

    Parameters
    ----------
    param : Union[float, Tuple[float, float], List[float]]
        Parameter value(s). Can be:
        - Single value: used for all pools
        - Tuple of (min, max): random value generated for each pool
        - List of values: must match n_pools
    n_pools : int
        Number of pools
    param_name : str
        Name of parameter for error messages

    Returns
    -------
    List[float]
        List of parameter values, one for each pool
    """
    if isinstance(param, (int, float)):
        return [param] * n_pools
    elif isinstance(param, tuple):
        return [np.random.uniform(*param) for _ in range(n_pools)]
    elif isinstance(param, list):
        if len(param) != n_pools:
            raise ValueError(
                f"Length of {param_name} list ({len(param)}) must match n_pools ({n_pools})"
            )
        return param
    else:
        raise TypeError(f"{param_name} must be a number, tuple, or list")


def create_sinusoid_current(
    n_pools: int,
    t_points: int,
    timestep_ms: float,
    amplitude_range: Union[Tuple[float, float], List[float]] = (100, 200),
    frequency_range: Union[Tuple[float, float], List[float]] = (1, 2),
    offset: Union[float, List[float]] = 120,
) -> INPUT_CURRENT__MATRIX:
    """Create a matrix of sinusoidal currents for multiple pools.

    Parameters
    ----------
    n_pools : int
        Number of current pools to generate
    t_points : int
        Number of time points
    timestep_ms : float
        Time step in milliseconds
    amplitude_range : Union[Tuple[float, float], List[float]]
        Range for amplitude selection (min, max) or list of amplitudes
    frequency_range : Union[Tuple[float, float], List[float]]
        Range for frequency selection in Hz (min, max) or list of frequencies
    offset : Union[float, List[float]]
        DC offset(s) to add to the sinusoid(s)

    Returns
    -------
    INPUT_CURRENT__MATRIX
        Matrix of shape (n_pools, t_points) containing sinusoidal currents
    """
    t = np.arange(0, t_points * timestep_ms, timestep_ms)
    input_current__matrix = np.zeros((n_pools, t_points))

    # Convert parameters to lists
    amplitudes = _validate_parameter_list(amplitude_range, n_pools, "amplitude_range")
    frequencies = _validate_parameter_list(frequency_range, n_pools, "frequency_range")
    offsets = _validate_parameter_list(offset, n_pools, "offset")

    for i in range(n_pools):
        phase = np.random.uniform(0, 2 * np.pi)
        input_current__matrix[i] = (
            amplitudes[i] * np.sin(2 * np.pi * frequencies[i] * t / 1000 + phase)
            + offsets[i]
        )

    return input_current__matrix


def create_sawtooth_current(
    n_pools: int,
    t_points: int,
    timestep_ms: float,
    amplitude_range: Union[Tuple[float, float], List[float]] = (100, 200),
    frequency_range: Union[Tuple[float, float], List[float]] = (1, 2),
    offset: Union[float, List[float]] = 120,
    width: Union[float, List[float]] = 0.5,
) -> INPUT_CURRENT__MATRIX:
    """Create a matrix of sawtooth currents for multiple pools.

    Parameters
    ----------
    n_pools : int
        Number of current pools to generate
    t_points : int
        Number of time points
    timestep_ms : float
        Time step in milliseconds
    amplitude_range : Union[Tuple[float, float], List[float]]
        Range for amplitude selection (min, max) or list of amplitudes
    frequency_range : Union[Tuple[float, float], List[float]]
        Range for frequency selection in Hz (min, max) or list of frequencies
    offset : Union[float, List[float]]
        DC offset(s) to add to the sawtooth(s)
    width : Union[float, List[float]]
        Width(s) of the rising edge as proportion of period (0 to 1)

    Returns
    -------
    INPUT_CURRENT__MATRIX
        Matrix of shape (n_pools, t_points) containing sawtooth currents
    """
    t = np.arange(0, t_points * timestep_ms, timestep_ms)
    input_current__matrix = np.zeros((n_pools, t_points))

    # Convert parameters to lists
    amplitudes = _validate_parameter_list(amplitude_range, n_pools, "amplitude_range")
    frequencies = _validate_parameter_list(frequency_range, n_pools, "frequency_range")
    offsets = _validate_parameter_list(offset, n_pools, "offset")
    widths = _validate_parameter_list(width, n_pools, "width")

    for i in range(n_pools):
        phase = np.random.uniform(0, 2 * np.pi)
        phase_t = 2 * np.pi * frequencies[i] * t / 1000 + phase
        sawtooth = (phase_t / (2 * np.pi)) % 1
        sawtooth = np.where(
            sawtooth < widths[i], sawtooth / widths[i], (1 - sawtooth) / (1 - widths[i])
        )
        input_current__matrix[i] = amplitudes[i] * sawtooth + offsets[i]

    return input_current__matrix


def create_step_current(
    n_pools: int,
    t_points: int,
    timestep_ms: float,
    step_heights: Optional[Union[List[float], List[List[float]]]] = None,
    step_durations: Optional[Union[List[float], List[List[float]]]] = None,
    offset: Union[float, List[float]] = 120,
) -> INPUT_CURRENT__MATRIX:
    """Create a matrix of step currents for multiple pools.

    Parameters
    ----------
    n_pools : int
        Number of current pools to generate
    t_points : int
        Number of time points
    timestep_ms : float
        Time step in milliseconds
    step_heights : Optional[Union[List[float], List[List[float]]]]
        List of step heights for all pools or list of lists (one per pool)
    step_durations : Optional[Union[List[float], List[List[float]]]]
        List of step durations in ms for all pools or list of lists (one per pool)
    offset : Union[float, List[float]]
        DC offset(s) to add to the steps

    Returns
    -------
    INPUT_CURRENT__MATRIX
        Matrix of shape (n_pools, t_points) containing step currents
    """
    input_current__matrix = np.zeros((n_pools, t_points))
    total_time = t_points * timestep_ms

    # Generate default step heights and durations if not provided
    if step_heights is None:
        step_heights = [
            [np.random.uniform(50, 200) for _ in range(3)] for _ in range(n_pools)
        ]
    elif isinstance(step_heights[0], (int, float)):
        step_heights = [step_heights] * n_pools

    if step_durations is None:
        step_durations = [
            [total_time / len(heights)] * len(heights) for heights in step_heights
        ]
    elif isinstance(step_durations[0], (int, float)):
        step_durations = [step_durations] * n_pools

    offsets = _validate_parameter_list(offset, n_pools, "offset")

    for i in range(n_pools):
        current = np.zeros(t_points)
        current_time = 0

        for height, duration in zip(step_heights[i], step_durations[i]):
            start_idx = int(current_time / timestep_ms)
            end_idx = int((current_time + duration) / timestep_ms)
            current[start_idx:end_idx] = height

        input_current__matrix[i] = current + offsets[i]

    return input_current__matrix


def create_ramp_current(
    n_pools: int,
    t_points: int,
    timestep_ms: float,
    start_range: Union[Tuple[float, float], List[float]] = (50, 100),
    end_range: Union[Tuple[float, float], List[float]] = (150, 200),
    offset: Union[float, List[float]] = 120,
) -> INPUT_CURRENT__MATRIX:
    """Create a matrix of ramp currents for multiple pools.

    Parameters
    ----------
    n_pools : int
        Number of current pools to generate
    t_points : int
        Number of time points
    timestep_ms : float
        Time step in milliseconds
    start_range : Union[Tuple[float, float], List[float]]
        Range for start current (min, max) or list of start currents
    end_range : Union[Tuple[float, float], List[float]]
        Range for end current (min, max) or list of end currents
    offset : Union[float, List[float]]
        DC offset(s) to add to the ramp(s)

    Returns
    -------
    INPUT_CURRENT__MATRIX
        Matrix of shape (n_pools, t_points) containing ramp currents
    """
    input_current__matrix = np.zeros((n_pools, t_points))

    # Convert parameters to lists
    start_currents = _validate_parameter_list(start_range, n_pools, "start_range")
    end_currents = _validate_parameter_list(end_range, n_pools, "end_range")
    offsets = _validate_parameter_list(offset, n_pools, "offset")

    for i in range(n_pools):
        ramp = np.linspace(start_currents[i], end_currents[i], t_points)
        input_current__matrix[i] = ramp + offsets[i]

    return input_current__matrix


def create_trapezoid_current(
    n_pools: int,
    t_points: int,
    timestep_ms: float,
    amplitude_range: Union[Tuple[float, float], List[float]] = (100, 200),
    rise_time_ms: Union[float, List[float]] = 100,
    plateau_time_ms: Union[float, List[float]] = 200,
    fall_time_ms: Union[float, List[float]] = 100,
    offset: Union[float, List[float]] = 120,
    delay_ms: Union[float, List[float]] = 0,
) -> INPUT_CURRENT__MATRIX:
    """Create a matrix of trapezoidal currents for multiple pools.

    Parameters
    ----------
    n_pools : int
        Number of current pools to generate
    t_points : int
        Number of time points
    timestep_ms : float
        Time step in milliseconds
    amplitude_range : Union[Tuple[float, float], List[float]]
        Range for amplitude selection (min, max) or list of amplitudes
    rise_time_ms : Union[float, List[float]]
        Duration(s) of the rising phase in milliseconds
    plateau_time_ms : Union[float, List[float]]
        Duration(s) of the plateau phase in milliseconds
    fall_time_ms : Union[float, List[float]]
        Duration(s) of the falling phase in milliseconds
    offset : Union[float, List[float]]
        DC offset(s) to add to the trapezoid(s)
    delay_ms : Union[float, List[float]]
        Delay before starting the trapezoid in milliseconds

    Returns
    -------
    INPUT_CURRENT__MATRIX
        Matrix of shape (n_pools, t_points) containing trapezoidal currents
    """
    input_current__matrix = np.zeros((n_pools, t_points))

    # Convert parameters to lists
    amplitudes = _validate_parameter_list(amplitude_range, n_pools, "amplitude_range")
    rise_times = _validate_parameter_list(rise_time_ms, n_pools, "rise_time_ms")
    plateau_times = _validate_parameter_list(
        plateau_time_ms, n_pools, "plateau_time_ms"
    )
    fall_times = _validate_parameter_list(fall_time_ms, n_pools, "fall_time_ms")
    offsets = _validate_parameter_list(offset, n_pools, "offset")
    delays = _validate_parameter_list(delay_ms, n_pools, "delay_ms")

    for i in range(n_pools):
        # Calculate indices for each phase
        delay_points = int(delays[i] / timestep_ms)
        rise_points = int(rise_times[i] / timestep_ms)
        plateau_points = int(plateau_times[i] / timestep_ms)
        fall_points = int(fall_times[i] / timestep_ms)

        # Create the base trapezoid shape
        trapezoid = np.zeros(t_points)

        # Calculate start indices for each phase
        rise_start = delay_points
        plateau_start = rise_start + rise_points
        fall_start = plateau_start + plateau_points
        end_idx = fall_start + fall_points

        # Ensure we don't exceed array bounds
        if rise_start < t_points:
            # Rising phase (linear ramp up)
            rise_end = min(plateau_start, t_points)
            if rise_end > rise_start:
                points_to_fill = rise_end - rise_start
                trapezoid[rise_start:rise_end] = np.linspace(0, 1, points_to_fill)

            # Plateau phase (constant)
            if plateau_start < t_points:
                plateau_end = min(fall_start, t_points)
                if plateau_end > plateau_start:
                    trapezoid[plateau_start:plateau_end] = 1

                # Falling phase (linear ramp down)
                if fall_start < t_points:
                    fall_end = min(end_idx, t_points)
                    if fall_end > fall_start:
                        points_to_fill = fall_end - fall_start
                        trapezoid[fall_start:fall_end] = np.linspace(
                            1, 0, points_to_fill
                        )

        input_current__matrix[i] = amplitudes[i] * trapezoid + offsets[i]

    return input_current__matrix
