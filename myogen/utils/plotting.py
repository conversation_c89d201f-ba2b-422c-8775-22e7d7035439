from pathlib import Path

import matplotlib.pyplot as plt
import numpy as np
from tqdm import tqdm

from myogen.simulators.emg.surface import MUAP_SHAPE__TENSOR, SURFACE_EMG__TENSOR
from myogen.simulators.spike_train import INPUT_CURRENT__MATRIX, SPIKE_TRAIN__MATRIX


def plot_spike_trains(
    save__path: Path, spike_trains__matrix: SPIKE_TRAIN__MATRIX, timestep__ms: float
):
    """
    Plot spike trains from neural pools.

    Parameters
    ----------
    save__path : Path
        Path to save the plot
    spike_trains__matrix : SPIKE_TRAIN__MATRIX
        Matrix of shape (n_pools, neurons_per_pool, t_points) containing spike trains
    timestep__ms : float
        Simulation timestep__ms in ms
    """
    # Determine dimensions from spike_trains
    n_pools, n_neurons, n_timepoints = spike_trains__matrix.shape

    # Create figure
    _, axes = plt.subplots(n_pools, 1, figsize=(14, 4 * n_pools), sharex=True)
    if n_pools == 1:
        axes = np.array([axes])

    for pool_idx in tqdm(range(n_pools), desc="Plotting spike trains"):
        ax = axes[pool_idx]

        # Create a colormap for visualization
        cmap = plt.cm.rainbow

        # Calculate spike counts for each neuron in this pool
        spike_counts = np.sum(spike_trains__matrix[pool_idx], axis=1)
        pool_min = np.min(spike_counts) if len(spike_counts) > 0 else 0
        pool_max = np.max(spike_counts) if len(spike_counts) > 0 else 1
        norm = plt.Normalize(vmin=pool_min, vmax=pool_max)

        # Amount of neurons with spikes
        n_neurons_with_spikes = np.sum(
            np.sum(spike_trains__matrix[pool_idx], axis=-1) > 0
        )

        # Plot spikes as vertical lines
        for neuron_idx in range(n_neurons):
            # Find spike times for this neuron
            spike_times = (
                np.where(spike_trains__matrix[pool_idx, neuron_idx])[0] * timestep__ms
            )

            # Skip if no spikes
            if len(spike_times) == 0:
                continue

            # Color based on spike count
            color = cmap(norm(spike_counts[neuron_idx]))

            # Plot vertical lines at spike times
            y_base = neuron_idx * 5  # Space between neurons
            ax.vlines(spike_times, y_base, y_base + 5, color=color, linewidth=1.5)

        # Set plot properties
        ax.set_xlim(0, n_timepoints * timestep__ms)
        ax.set_ylim(-1, n_neurons_with_spikes * 5 + 1)
        ax.set_title(f"Pool {pool_idx + 1} Spike Trains")
        ax.set_xlabel("Time (ms)")
        ax.set_ylabel("Neuron")

    plt.tight_layout()
    plt.savefig(save__path / "spike_trains.png")
    plt.close()


def plot_input_currents(
    save__path: Path, input_current__matrix: INPUT_CURRENT__MATRIX, timestep__ms: float
):
    """
    Plot the input currents.

    Parameters
    ----------
    save__path: Path
        Path to save the plot
    input_current__matrix: INPUT_CURRENT__MATRIX
        Matrix of shape (n_pools, t_points) containing current values
        Each row represents the current for one pool
    timestep__ms: float
        Simulation timestep__ms in ms
    """
    n_pools = input_current__matrix.shape[0]

    t = np.arange(0, input_current__matrix.shape[1] * timestep__ms, timestep__ms)
    _, axes = plt.subplots(n_pools, 1, figsize=(10, 2 * n_pools), sharex=True)

    if n_pools == 1:
        axes = [axes]

    for i, (current, ax) in enumerate(zip(input_current__matrix, axes)):
        ax.plot(t, current, "o")
        ax.set_title(f"Pool {i + 1} Input Current")
        ax.set_xlabel("Time (ms)")
        ax.set_ylabel("Current (nA)")

    plt.tight_layout()
    plt.savefig(save__path / "input_currents.png")
    plt.close()


def plot_muaps(save__path: Path, muap_shapes__tensor: MUAP_SHAPE__TENSOR):
    """
    Plot the MUAPs.

    Parameters
    ----------
    save__path: Path
        Path to save the plot
    muap_shapes__tensor: MUAP_SHAPE__TENSOR
        Tensor of shape (n_muaps, n_rows, n_cols, n_samples) containing MUAPs
    """
    for muap_idx in tqdm(range(muap_shapes__tensor.shape[0]), desc="Plotting MUAPs"):
        _, ax = plt.subplots(
            muap_shapes__tensor.shape[1],
            muap_shapes__tensor.shape[2],
            figsize=(
                muap_shapes__tensor.shape[1] * 2,
                muap_shapes__tensor.shape[2] * 2,
            ),
        )
        for row_idx in range(muap_shapes__tensor.shape[1]):
            for col_idx in range(muap_shapes__tensor.shape[2]):
                ax[row_idx, col_idx].plot(
                    muap_shapes__tensor[muap_idx, row_idx, col_idx]
                )
        plt.savefig(save__path / f"muap_{muap_idx}.png")
        plt.close()


def plot_muscle_distribution(
    save__path: Path,
    pos_MUs: list[tuple[float, float]],
    r_MUs: list[float],
    r: float,
    pos_Fibs: list[list[tuple[float, float]]],
    mean_fib_radius: float,
):
    """
    Plot the distribution of motor units and muscle fibers in the muscle.

    Parameters
    ----------
    save__path: Path
        Path to save the plot
    pos_MUs : list
        List of motor unit positions
    r_MUs : list
        List of motor unit radii
    r : float
        Muscle radius
    pos_Fibs : list
        List of fiber positions for each motor unit
    mean_fib_radius : float
        Mean radius of muscle fibers
    """
    _, ax = plt.subplots(figsize=(10, 10))
    ax.add_patch(plt.Circle((0, 0), r, color="r", alpha=0.1))

    for i in range(len(pos_MUs)):
        ax.add_patch(plt.Circle(pos_MUs[i], r_MUs[i], color="r", alpha=0.5))
        for j in range(len(pos_Fibs[i])):
            pos_fib = (
                pos_MUs[i][0] + pos_Fibs[i][j][0],
                pos_MUs[i][1] + pos_Fibs[i][j][1],
            )
            ax.add_patch(plt.Circle(pos_fib, mean_fib_radius, color="b", alpha=1))

    ax.set_aspect("equal", adjustable="datalim")
    ax.plot()  # Causes an autoscale update.
    plt.savefig(save__path / "muscle_distribution.png")
    plt.close()


def plot_surface_emg(save__path: Path, surface_emg__tensor: SURFACE_EMG__TENSOR):
    """
    Plot the EMG signal.

    Parameters
    ----------
    save__path: Path
        Path to save the plot
    surface_emg__tensor: SURFACE_EMG__TENSOR
        Tensor of shape (n_pools, n_rows, n_cols, n_time) containing EMG signals
    """
    for pool_idx in tqdm(
        range(surface_emg__tensor.shape[0]), desc="Plotting surface EMG"
    ):
        _, ax = plt.subplots(
            surface_emg__tensor.shape[1],
            surface_emg__tensor.shape[2],
            figsize=(
                surface_emg__tensor.shape[1] * 2,
                surface_emg__tensor.shape[2] * 2,
            ),
        )

        for row_idx in range(surface_emg__tensor.shape[1]):
            for col_idx in range(surface_emg__tensor.shape[2]):
                ax[row_idx, col_idx].plot(
                    surface_emg__tensor[pool_idx, row_idx, col_idx]
                )
        plt.savefig(save__path / f"emg_pool_{pool_idx}.svg")
        plt.close()
